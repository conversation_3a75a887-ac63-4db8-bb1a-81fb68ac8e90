{"name": "claude-chat", "version": "1.0.0", "type": "module", "dependencies": {"@botanicastudios/claude-code-sdk-ts": "github:botanicastudios/claude-code-sdk-ts", "@instantlyeasy/claude-code-sdk-ts": "latest", "axios": "^1.6.0", "better-sqlite3": "^12.2.0", "express": "^4.18.0", "path-exists": "^5.0.0"}, "devDependencies": {"@types/better-sqlite3": "^7.6.13", "@types/express": "^4.17.0", "@types/node": "^20.0.0", "tsx": "^4.0.0", "typescript": "^5.0.0"}, "scripts": {"dev": "tsx src/server.ts", "build": "tsc"}}