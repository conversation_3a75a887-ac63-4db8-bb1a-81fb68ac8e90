import express from 'express';
import { ChatService } from './chat.js';
import { AudioService } from './audio.js';
import { getDatabase } from './database.js';
import fs from 'fs';
import path from 'path';
import os from 'os';

const app = express();
const chatService = new ChatService();
const audioService = new AudioService();
const db = getDatabase(); // Initialize database

app.use(express.json());
app.use(express.static('public'));

app.post('/api/chat-stream', async (req, res) => {
  try {
    const { message } = req.body;
    console.log('🌊 SSE streaming chat request:', message);
    
    // Set SSE headers
    res.writeHead(200, {
      'Content-Type': 'text/event-stream',
      'Cache-Control': 'no-cache',
      'Connection': 'keep-alive',
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Headers': 'Cache-Control'
    });

    let fullResponse = '';
    let chunkCount = 0;
    
    try {
      console.log('🚀 Calling chatService.sendMessage with SSE...');
      
      const result = await chatService.sendMessage(
        message, 
        (textSegment) => {
          chunkCount++;
          console.log(`📦 SSE sending text segment ${chunkCount}:`, {
            length: textSegment.length,
            preview: textSegment.substring(0, 30) + '...'
          });
          fullResponse += textSegment;
          
          // Send text segment immediately via SSE
          const data = JSON.stringify({
            type: 'text_segment',
            content: textSegment,
            segmentNumber: chunkCount
          });
          res.write(`data: ${data}\n\n`);
        },
        (toolUse) => {
          console.log(`🔧 SSE sending tool use: ${toolUse.name}`);
          
          // Send tool use immediately via SSE
          const data = JSON.stringify({
            type: 'tool_use',
            toolUse: toolUse
          });
          res.write(`data: ${data}\n\n`);
        },
        (toolResult) => {
          console.log(`📋 SSE sending tool result: ${toolResult.isError ? 'ERROR' : 'SUCCESS'}`);
          
          // Send tool result immediately via SSE
          const data = JSON.stringify({
            type: 'tool_result',
            toolResult: toolResult
          });
          res.write(`data: ${data}\n\n`);
        }
      );
      
      console.log('✅ SSE streaming completed on server');
      console.log('📊 SSE statistics:', {
        totalSegments: chunkCount,
        fullResponseLength: fullResponse.length,
        toolUses: result.toolUses.length
      });

      // For new sessions, wait for session ID capture to avoid race condition
      // For resumed sessions, session ID is already available, so no wait needed
      let finalSessionId = chatService.currentSessionId;
      let finalForkedSessionId = chatService.currentForkedSessionId;

      if (!finalSessionId && !chatService.isResumedSession) {
        // This is a new session - wait for session ID capture with timeout
        console.log('⏳ New session detected, waiting for session ID capture...');
        console.log('🔍 Current state - sessionId:', chatService.currentSessionId, 'isResumedSession:', chatService.isResumedSession);
        const waitStart = Date.now();
        const maxWaitTime = 3000; // 3 second timeout

        while (!chatService.currentSessionId && (Date.now() - waitStart) < maxWaitTime) {
          await new Promise(resolve => setTimeout(resolve, 50));
        }

        const waitTime = Date.now() - waitStart;
        finalSessionId = chatService.currentSessionId;
        finalForkedSessionId = chatService.currentForkedSessionId;

        if (finalSessionId) {
          console.log(`✅ Session ID captured after ${waitTime}ms wait:`, finalSessionId);
        } else {
          console.warn(`⚠️ Session ID not captured within ${waitTime}ms timeout, proceeding anyway`);
        }
      } else if (finalSessionId) {
        console.log('✅ Session ID already available (resumed session):', finalSessionId);
      } else {
        console.log('🔍 No session ID and resumed session - this should not happen');
      }

      // Send completion event
      const completionData = JSON.stringify({
        type: 'complete',
        sessionId: finalSessionId,
        forkedSessionId: finalForkedSessionId,
        totalSegments: chunkCount
      });
      res.write(`data: ${completionData}\n\n`);
      res.end();
      
    } catch (error: any) {
      console.error('❌ SSE streaming error:', error);
      const errorData = JSON.stringify({
        type: 'error',
        error: error.message
      });
      res.write(`data: ${errorData}\n\n`);
      res.end();
    }
    
  } catch (error: any) {
    console.error('❌ SSE setup error:', error);
    const errorData = JSON.stringify({
      type: 'error',
      error: error.message
    });
    res.write(`data: ${errorData}\n\n`);
    res.end();
  }
});

app.post('/api/chat', async (req, res) => {
  try {
    const { message } = req.body;
    console.log('🌊 Streaming chat request:', message);
    
    let fullResponse = '';
    const chunks: string[] = [];
    let chunkCount = 0;
    
    try {
      console.log('🚀 Calling chatService.sendMessage...');
      
      const result = await chatService.sendMessage(message, (chunk) => {
        chunkCount++;
        console.log(`📦 Server received chunk ${chunkCount}:`, {
          length: chunk.length,
          preview: chunk.substring(0, 30) + '...'
        });
        fullResponse += chunk;
        chunks.push(chunk);
      });
      
      const response = result.response;
      const toolUses = result.toolUses;
      
      console.log('✅ Streaming completed on server');
      console.log('📊 Server statistics:', {
        totalChunks: chunks.length,
        fullResponseLength: fullResponse.length,
        responseLength: response.length
      });
      
      const responseData = { 
        type: 'streaming',
        chunks: chunks,
        fullResponse: fullResponse,
        toolUses: toolUses,
        sessionId: chatService.currentSessionId,
        success: true 
      };
      
      console.log('📤 Sending response to frontend:', {
        type: responseData.type,
        chunksCount: responseData.chunks.length,
        fullResponseLength: responseData.fullResponse.length,
        success: responseData.success
      });
      
      // Return all chunks at once for now (simpler approach)
      res.json(responseData);
      
    } catch (error: any) {
      console.error('❌ Streaming error:', error);
      res.status(500).json({ 
        type: 'error', 
        error: error.message 
      });
    }
    
  } catch (error: any) {
    console.error('❌ Streaming setup error:', error);
    res.status(500).json({ error: error.message });
  }
});

app.post('/api/speak', async (req, res) => {
  try {
    const { text } = req.body;
    // Just return the TTS service URL - let browser fetch directly
    const ttsUrl = `http://localhost:8001/synthesize`;
    res.json({ 
      ttsUrl,
      text,
      timestamp: Date.now() // Cache busting
    });
  } catch (error: any) {
    res.status(500).json({ error: error.message });
  }
});

app.post('/api/load-session', async (req, res) => {
  try {
    const { project, sessionId } = req.body;
    
    if (!project || !sessionId) {
      return res.status(400).json({ error: 'Project and sessionId are required' });
    }
    
    console.log(`🔍 Loading session: ${project}/${sessionId}`);
    
    // Construct path to Claude projects folder
    const projectsPath = path.join(os.homedir(), '.claude', 'projects');
    const sessionFilePath = path.join(projectsPath, project, `${sessionId}.jsonl`);
    
    console.log(`📁 Looking for: ${sessionFilePath}`);
    
    // Validate session file exists
    if (!fs.existsSync(sessionFilePath)) {
      return res.status(404).json({ error: 'Session file not found' });
    }
    
    // Read and parse JSONL file
    const fileContent = fs.readFileSync(sessionFilePath, 'utf8');
    const lines = fileContent.trim().split('\n');
    const messages = [];
    
    for (const line of lines) {
      if (!line.trim()) continue;
      
      try {
        const sessionData = JSON.parse(line);
        
        // Extract user and assistant messages
        if (sessionData.type === 'user' && sessionData.message?.role === 'user') {
          messages.push({
            role: 'user',
            content: sessionData.message.content,
            timestamp: sessionData.timestamp
          });
        } else if (sessionData.type === 'assistant' && sessionData.message?.role === 'assistant') {
          // Handle assistant messages with content array
          let content = '';
          if (Array.isArray(sessionData.message.content)) {
            // Extract text content, ignore tool_use
            const textContent = sessionData.message.content.find((c: any) => c.type === 'text');
            content = textContent ? textContent.text : '';
          } else if (typeof sessionData.message.content === 'string') {
            content = sessionData.message.content;
          }
          
          if (content) {
            messages.push({
              role: 'assistant',
              content: content,
              timestamp: sessionData.timestamp
            });
          }
        }
      } catch (parseError) {
        console.warn(`⚠️ Failed to parse line: ${line.substring(0, 100)}...`);
      }
    }
    
    console.log(`✅ Loaded ${messages.length} messages from session`);
    
    // Set the session ID in chat service for continuation
    chatService.setSessionId(sessionId);
    
    res.json({ 
      messages,
      sessionId,
      loaded: true
    });
    
  } catch (error: any) {
    console.error('❌ Error loading session:', error);
    res.status(500).json({ error: error.message });
  }
});

app.post('/api/clear-session', async (req, res) => {
  try {
    console.log('🧹 Clearing session');
    
    // Clear the session ID in chat service
    chatService.clearSession();
    
    res.json({ 
      cleared: true,
      message: 'Session cleared successfully'
    });
    
  } catch (error: any) {
    console.error('❌ Error clearing session:', error);
    res.status(500).json({ error: error.message });
  }
});

// Project Management API Endpoints

// GET /api/projects - List all projects with sessions
app.get('/api/projects', async (req, res) => {
  try {
    console.log('📋 Fetching all projects');
    const projects = db.getProjects();
    
    res.json({
      projects,
      count: projects.length
    });
    
  } catch (error: any) {
    console.error('❌ Error fetching projects:', error);
    res.status(500).json({ error: error.message });
  }
});

// POST /api/projects - Create new project
app.post('/api/projects', async (req, res) => {
  try {
    const { name, path: projectPath } = req.body;
    
    if (!name || !projectPath) {
      return res.status(400).json({ 
        error: 'Name and path are required' 
      });
    }
    
    console.log(`🆕 Creating new project: ${name} at ${projectPath}`);
    
    // Extract project name from path if not provided or if it's just the folder name
    let projectName = name;
    if (!projectName || projectName === path.basename(projectPath)) {
      projectName = path.basename(projectPath);
    }
    
    const project = db.createProject(projectName, projectPath);
    
    console.log('✅ Project created:', project);
    res.status(201).json(project);
    
  } catch (error: any) {
    console.error('❌ Error creating project:', error);
    
    if (error.message.includes('already exists')) {
      res.status(409).json({ error: error.message });
    } else if (error.message.includes('Invalid')) {
      res.status(400).json({ error: error.message });
    } else {
      res.status(500).json({ error: error.message });
    }
  }
});

// DELETE /api/projects/:id - Delete project and all sessions
app.delete('/api/projects/:id', async (req, res) => {
  try {
    const projectId = parseInt(req.params.id);
    
    if (isNaN(projectId)) {
      return res.status(400).json({ 
        error: 'Invalid project ID' 
      });
    }
    
    console.log(`🗑️ Deleting project ID: ${projectId}`);
    
    const success = db.deleteProject(projectId);
    
    if (!success) {
      return res.status(404).json({ 
        error: 'Project not found' 
      });
    }
    
    console.log('✅ Project deleted successfully');
    res.json({ 
      deleted: true,
      message: 'Project and all sessions deleted successfully'
    });
    
  } catch (error: any) {
    console.error('❌ Error deleting project:', error);
    res.status(500).json({ error: error.message });
  }
});

// Session Management API Endpoints

// POST /api/projects/:id/sessions - Create new session
app.post('/api/projects/:id/sessions', async (req, res) => {
  try {
    const projectId = parseInt(req.params.id);
    const { name } = req.body;
    
    if (isNaN(projectId)) {
      return res.status(400).json({ 
        error: 'Invalid project ID' 
      });
    }
    
    if (!name) {
      return res.status(400).json({ 
        error: 'Session name is required' 
      });
    }
    
    console.log(`🆕 Creating new session: ${name} for project ${projectId}`);
    
    const session = db.createSession(projectId, name.trim());
    
    console.log('✅ Session created:', session);
    res.status(201).json(session);
    
  } catch (error: any) {
    console.error('❌ Error creating session:', error);
    
    if (error.message.includes('already exists')) {
      res.status(409).json({ error: error.message });
    } else if (error.message.includes('Invalid') || error.message.includes('not found')) {
      res.status(400).json({ error: error.message });
    } else {
      res.status(500).json({ error: error.message });
    }
  }
});

// DELETE /api/sessions/:id - Delete session
app.delete('/api/sessions/:id', async (req, res) => {
  try {
    const sessionId = parseInt(req.params.id);
    
    if (isNaN(sessionId)) {
      return res.status(400).json({ 
        error: 'Invalid session ID' 
      });
    }
    
    console.log(`🗑️ Deleting session ID: ${sessionId}`);
    
    const success = db.deleteSession(sessionId);
    
    if (!success) {
      return res.status(404).json({ 
        error: 'Session not found' 
      });
    }
    
    console.log('✅ Session deleted successfully');
    res.json({ 
      deleted: true,
      message: 'Session deleted successfully'
    });
    
  } catch (error: any) {
    console.error('❌ Error deleting session:', error);
    res.status(500).json({ error: error.message });
  }
});

// PUT /api/sessions/:id/session-id - Update Claude session ID
app.put('/api/sessions/:id/session-id', async (req, res) => {
  try {
    const sessionId = parseInt(req.params.id);
    const { sessionId: claudeSessionId } = req.body;
    
    if (isNaN(sessionId)) {
      return res.status(400).json({ 
        error: 'Invalid session ID' 
      });
    }
    
    if (!claudeSessionId) {
      return res.status(400).json({ 
        error: 'Claude session ID is required' 
      });
    }
    
    console.log(`🔄 Updating session ${sessionId} with Claude session ID: ${claudeSessionId}`);
    
    const success = db.updateSessionId(sessionId, claudeSessionId);
    
    if (!success) {
      return res.status(404).json({ 
        error: 'Session not found' 
      });
    }
    
    console.log('✅ Session ID updated successfully');
    res.json({ 
      updated: true,
      sessionId: claudeSessionId
    });
    
  } catch (error: any) {
    console.error('❌ Error updating session ID:', error);
    res.status(500).json({ error: error.message });
  }
});

// Path Validation Endpoint
app.get('/api/validate-path', async (req, res) => {
  try {
    const { path: pathToValidate } = req.query;
    
    if (!pathToValidate || typeof pathToValidate !== 'string') {
      return res.status(400).json({ 
        error: 'Path parameter is required' 
      });
    }
    
    console.log(`🔍 Validating path: ${pathToValidate}`);
    
    const isValid = db.validateProjectPath(pathToValidate);
    
    res.json({ 
      valid: isValid,
      path: pathToValidate,
      exists: fs.existsSync(pathToValidate),
      isDirectory: isValid
    });
    
  } catch (error: any) {
    console.error('❌ Error validating path:', error);
    res.status(500).json({ error: error.message });
  }
});

// Set Project Path Endpoint (for ChatService)
app.post('/api/set-project-path', async (req, res) => {
  try {
    const { path: projectPath } = req.body;
    
    if (!projectPath || typeof projectPath !== 'string') {
      return res.status(400).json({ 
        error: 'Project path is required' 
      });
    }
    
    console.log('📁 Setting project path for ChatService:', projectPath);
    
    // Set the project path in the chat service
    chatService.setProjectPath(projectPath);
    
    res.json({ 
      success: true,
      projectPath: projectPath
    });
    
  } catch (error: any) {
    console.error('❌ Error setting project path:', error);
    res.status(500).json({ error: error.message });
  }
});

// Set Active Session Endpoint (for session switching)
app.post('/api/set-active-session', async (req, res) => {
  try {
    const { sessionId, claudeSessionId } = req.body;
    
    if (!sessionId || typeof sessionId !== 'number') {
      return res.status(400).json({ 
        error: 'Session ID is required and must be a number' 
      });
    }
    
    console.log('🎯 Setting active session for ChatService:', sessionId, 'Claude session:', claudeSessionId);
    
    // Set the active session in the chat service
    chatService.setActiveSession(sessionId, claudeSessionId || null);
    
    res.json({ 
      success: true,
      sessionId: sessionId,
      claudeSessionId: claudeSessionId || null
    });
    
  } catch (error: any) {
    console.error('❌ Error setting active session:', error);
    res.status(500).json({ error: error.message });
  }
});

app.listen(3000, () => {
  console.log('Chat app running on http://localhost:3000');
  console.log('📊 Database Stats:', db.getStats());
});