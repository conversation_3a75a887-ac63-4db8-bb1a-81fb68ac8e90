import Database from 'better-sqlite3';
import { pathExists } from 'path-exists';
import path from 'path';
import fs from 'fs';
import os from 'os';

export interface Project {
  id: number;
  name: string;
  path: string;
  created_at: string;
}

export interface Session {
  id: number;
  project_id: number;
  name: string;
  session_id: string | null;
  created_at: string;
  updated_at: string;
}

export interface ProjectWithSessions extends Project {
  sessions: Session[];
}

export class DatabaseService {
  private db: Database.Database;
  private dbPath: string;

  constructor() {
    // Create ~/.claude-chat directory if it doesn't exist
    const claudeChatDir = path.join(os.homedir(), '.claude-chat');
    if (!fs.existsSync(claudeChatDir)) {
      fs.mkdirSync(claudeChatDir, { recursive: true });
    }

    this.dbPath = path.join(claudeChatDir, 'database.sqlite');
    this.db = new Database(this.dbPath);
    
    // Enable foreign keys
    this.db.pragma('foreign_keys = ON');
    
    this.initialize();
  }

  private initialize(): void {
    console.log('🗄️ Initializing database at:', this.dbPath);
    
    // Run migrations
    this.runMigrations();
    
    console.log('✅ Database initialized successfully');
  }

  private runMigrations(): void {
    // Check if we need to run initial migration
    const tableExists = this.db.prepare(`
      SELECT name FROM sqlite_master 
      WHERE type='table' AND name='projects'
    `).get();

    if (!tableExists) {
      console.log('🚀 Running initial database migration...');
      
      // Create projects table
      this.db.exec(`
        CREATE TABLE projects (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          name TEXT NOT NULL,
          path TEXT NOT NULL UNIQUE,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
      `);

      // Create sessions table
      this.db.exec(`
        CREATE TABLE sessions (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          project_id INTEGER NOT NULL,
          name TEXT NOT NULL,
          session_id TEXT,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (project_id) REFERENCES projects(id) ON DELETE CASCADE,
          UNIQUE(project_id, name)
        )
      `);

      // Create indexes
      this.db.exec(`
        CREATE INDEX idx_sessions_project_id ON sessions(project_id);
        CREATE INDEX idx_projects_path ON projects(path);
      `);

      console.log('✅ Initial migration completed');
    }
  }

  // Project CRUD operations
  createProject(name: string, projectPath: string): Project {
    if (!this.validateProjectPath(projectPath)) {
      throw new Error(`Invalid project path: ${projectPath}`);
    }

    if (!this.validateProjectName(name)) {
      throw new Error(`Invalid project name: ${name}`);
    }

    try {
      const stmt = this.db.prepare(`
        INSERT INTO projects (name, path) 
        VALUES (?, ?)
      `);
      
      const result = stmt.run(name, projectPath);
      
      return this.getProject(result.lastInsertRowid as number)!;
    } catch (error: any) {
      if (error.code === 'SQLITE_CONSTRAINT_UNIQUE') {
        throw new Error(`Project already exists at path: ${projectPath}`);
      }
      throw error;
    }
  }

  getProjects(): ProjectWithSessions[] {
    const projects = this.db.prepare(`
      SELECT * FROM projects 
      ORDER BY created_at DESC
    `).all() as Project[];

    return projects.map(project => ({
      ...project,
      sessions: this.getSessionsByProject(project.id)
    }));
  }

  getProject(id: number): Project | null {
    return this.db.prepare(`
      SELECT * FROM projects WHERE id = ?
    `).get(id) as Project | null;
  }

  deleteProject(id: number): boolean {
    const result = this.db.prepare(`
      DELETE FROM projects WHERE id = ?
    `).run(id);
    
    return result.changes > 0;
  }

  // Session CRUD operations
  createSession(projectId: number, name: string): Session {
    if (!this.validateSessionName(name)) {
      throw new Error(`Invalid session name: ${name}`);
    }

    // Verify project exists
    const project = this.getProject(projectId);
    if (!project) {
      throw new Error(`Project with id ${projectId} not found`);
    }

    try {
      const stmt = this.db.prepare(`
        INSERT INTO sessions (project_id, name) 
        VALUES (?, ?)
      `);
      
      const result = stmt.run(projectId, name);
      
      return this.getSession(result.lastInsertRowid as number)!;
    } catch (error: any) {
      if (error.code === 'SQLITE_CONSTRAINT_UNIQUE') {
        throw new Error(`Session "${name}" already exists in this project`);
      }
      throw error;
    }
  }

  getSessionsByProject(projectId: number): Session[] {
    return this.db.prepare(`
      SELECT * FROM sessions 
      WHERE project_id = ? 
      ORDER BY created_at DESC
    `).all(projectId) as Session[];
  }

  getSession(id: number): Session | null {
    return this.db.prepare(`
      SELECT * FROM sessions WHERE id = ?
    `).get(id) as Session | null;
  }

  updateSessionId(id: number, sessionId: string): boolean {
    const result = this.db.prepare(`
      UPDATE sessions 
      SET session_id = ?, updated_at = CURRENT_TIMESTAMP 
      WHERE id = ?
    `).run(sessionId, id);
    
    return result.changes > 0;
  }

  deleteSession(id: number): boolean {
    const result = this.db.prepare(`
      DELETE FROM sessions WHERE id = ?
    `).run(id);
    
    return result.changes > 0;
  }

  // Validation methods
  validateProjectPath(projectPath: string): boolean {
    try {
      // Check if path exists and is a directory
      if (!fs.existsSync(projectPath)) {
        return false;
      }
      
      const stats = fs.statSync(projectPath);
      return stats.isDirectory();
    } catch {
      return false;
    }
  }

  validateProjectName(name: string): boolean {
    // Basic validation - name should be non-empty and reasonable length
    return name.trim().length > 0 && name.trim().length <= 100;
  }

  validateSessionName(name: string): boolean {
    // Session name constraints: 1-50 chars, alphanumeric + spaces, hyphens, underscores
    const trimmed = name.trim();
    
    if (trimmed.length < 1 || trimmed.length > 50) {
      return false;
    }
    
    // Allow letters, numbers, spaces, hyphens, underscores
    const validPattern = /^[a-zA-Z0-9\s\-_]+$/;
    return validPattern.test(trimmed);
  }

  // Utility methods
  close(): void {
    this.db.close();
  }

  // Get database stats for debugging
  getStats(): { projects: number; sessions: number } {
    const projectCount = this.db.prepare('SELECT COUNT(*) as count FROM projects').get() as { count: number };
    const sessionCount = this.db.prepare('SELECT COUNT(*) as count FROM sessions').get() as { count: number };
    
    return {
      projects: projectCount.count,
      sessions: sessionCount.count
    };
  }
}

// Singleton instance
let dbInstance: DatabaseService | null = null;

export function getDatabase(): DatabaseService {
  if (!dbInstance) {
    dbInstance = new DatabaseService();
  }
  return dbInstance;
}