import axios from 'axios';

export class AudioService {
  private ttsBaseUrl = 'http://localhost:8001';

  async synthesizeSpeech(text: string): Promise<string> {
    try {
      const response = await axios.post(`${this.ttsBaseUrl}/synthesize`, {
        text
      }, {
        responseType: 'arraybuffer'  // Changed from blob to arraybuffer
      });

      // Convert arraybuffer to blob, then to object URL
      const blob = new Blob([response.data], { type: 'audio/wav' });
      const audioUrl = URL.createObjectURL(blob);
      
      console.log('🎵 Audio URL created:', audioUrl);
      return audioUrl;
    } catch (error) {
      console.error('TTS error:', error);
      throw new Error('Failed to generate speech');
    }
  }
}