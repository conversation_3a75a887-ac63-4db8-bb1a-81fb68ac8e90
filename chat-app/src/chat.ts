import { claude } from '@botanicastudios/claude-code-sdk-ts';

export class ChatService {
  private sessionId: string | null = null;
  private builder = claude();
  private conversation: any = null; // Persistent conversation instance
  private projectPath: string | null = null; // Dynamic project path
  private currentDbSessionId: number | null = null; // Track which DB session is active
  private forkedSessionId: string | null = null; // Store forked session ID for future resume

  // Getter for sessionId (for server access)
  get currentSessionId(): string | null {
    return this.sessionId;
  }

  // Getter for forked session ID (for database updates)
  get currentForkedSessionId(): string | null {
    return this.forkedSessionId;
  }

  // Set project path and session context
  async setProjectPath(path: string): Promise<void> {
    console.log('📁 Setting project path:', path);
    
    // Only clear session if path actually changed
    if (this.projectPath !== path) {
      console.log('🔄 Project path changed from', this.projectPath, 'to', path);
      this.projectPath = path;
      
      // If we have an active conversation, we need to restart it with the new path
      if (this.conversation) {
        console.log('🔄 Restarting conversation with new project path');
        await this.clearSession();
      }
    } else {
      console.log('✅ Project path unchanged, keeping existing conversation');
    }
  }

  // Set the active session context (called when switching sessions)
  async setActiveSession(dbSessionId: number, claudeSessionId: string | null): Promise<void> {
    console.log('🎯 Setting active session:', dbSessionId, 'with Claude session:', claudeSessionId);
    
    // Check if we're switching to a different session
    if (this.currentDbSessionId !== dbSessionId) {
      console.log('🔄 Session changed from', this.currentDbSessionId, 'to', dbSessionId);
      this.currentDbSessionId = dbSessionId;
      
      // Clear existing conversation to load the new session
      if (this.conversation) {
        console.log('🔄 Clearing conversation for session switch');
        await this.clearSession();
      }
      
      // Set the Claude session ID if we have one (for resuming)
      if (claudeSessionId) {
        this.sessionId = claudeSessionId;
        this.isLoadedSession = true;
        console.log('✅ Will resume existing Claude session:', claudeSessionId);
      } else {
        // New session - reset flags
        this.sessionId = null;
        this.isLoadedSession = false;
        this.forkedSessionId = null;
        console.log('🆕 New session - flags reset for fresh start');
      }
    } else {
      console.log('✅ Same session selected, keeping existing conversation');
    }
  }

  // Get current project path
  get currentProjectPath(): string | null {
    return this.projectPath;
  }

  // Current message tracking (for persistent stream handler)
  private currentMessageResponse = '';
  private currentMessageResolved = false;
  private currentChunkCount = 0;
  private currentOnChunk: ((chunk: string) => void) | null = null;
  private currentOnToolUse: ((toolUse: {name: string, input: any}) => void) | null = null;
  private currentOnToolResult: ((toolResult: {content: string, isError: boolean}) => void) | null = null;
  private currentToolUses: Array<{name: string, input: any}> = [];
  private isLoadedSession: boolean = false; // Track if session was loaded vs fresh

  // Getter for resumed session status (for server access)
  get isResumedSession(): boolean {
    return this.isLoadedSession;
  }

  async sendMessage(message: string, onChunk: (chunk: string) => void, onToolUse?: (toolUse: {name: string, input: any}) => void, onToolResult?: (toolResult: {content: string, isError: boolean}) => void): Promise<{response: string, toolUses: Array<{name: string, input: any}>}> {
    try {
      console.log('🌊 ChatService.sendMessageStreaming called (PERSISTENT + STREAMING mode)');
      console.log('📝 Message:', message);
      console.log('🆔 Current sessionId:', this.sessionId);
      console.log('💼 Conversation exists:', !!this.conversation);

      // Reset variables for this new message
      this.currentMessageResponse = '';
      this.currentMessageResolved = false;
      this.currentChunkCount = 0;
      this.currentOnChunk = onChunk;
      this.currentOnToolUse = onToolUse || null;
      this.currentOnToolResult = onToolResult || null;
      this.currentToolUses = [];

      // Create persistent conversation if it doesn't exist
      if (!this.conversation) {
        if (this.sessionId && this.isLoadedSession) {
          // Resume existing session
          console.log('🔄 Resuming loaded session with ID:', this.sessionId);
          try {
            console.log('❤️ sending in ', this.sessionId);
            this.conversation = this.builder
              .inDirectory(this.projectPath || '/Users/<USER>/Documents/gennext')
              .skipPermissions()
              .withSessionId(this.sessionId) // Use sessionId to resume
              .asConversation()
              .keepAlive();
            console.log('✅ Loaded session conversation created successfully');
          } catch (error) {
            console.warn('⚠️ Failed to resume session, creating fresh conversation:');
            // Fallback to fresh session
            this.conversation = this.builder
              .inDirectory(this.projectPath || '/Users/<USER>/Documents/gennext')
              .skipPermissions()
              .asConversation()
              .keepAlive(true);
            this.isLoadedSession = false;
          }
        } else {
          // Create fresh session (current behavior)
          console.log('🆕 Creating new persistent conversation with keepAlive()');
          this.conversation = this.builder
            .inDirectory(this.projectPath || '/Users/<USER>/Documents/gennext')
            .skipPermissions()
            .asConversation()
            .keepAlive(true);
        }

        // Set up streaming handler ONCE when conversation is created (like the example)
        console.log('🎬 Setting up persistent stream handler...');
        this.conversation.stream((msg: any, sessionId: string) => {
          console.log(`📦 Received stream message - FULL DATA:`, {
            msg: JSON.stringify(msg, null, 2),
            sessionId: sessionId
          });

          // Handle session ID capture and fork detection
          if (!this.sessionId && sessionId) {
            // Fresh session - capture the new session ID
            this.sessionId = sessionId;
            console.log('✅ SessionId captured from stream (fresh session):', this.sessionId);
          } else if (this.isLoadedSession && sessionId) {
            // Resumed session - check for fork
            if (this.sessionId !== sessionId) {
              console.log('🔀 Session fork detected! Old:', this.sessionId, 'New:', sessionId);
              console.log('📝 Storing new forked session ID for future resume');
              // Store the new forked session ID for future resume (don't change current conversation)
              this.forkedSessionId = sessionId;
            } else {
              console.log('✅ SessionId confirmed for loaded session:', sessionId);
            }
          }

          if (msg.type === 'assistant') {
            console.log('✅ Processing assistant message');
            this.currentChunkCount++;

            // Handle content array format
            if (msg.content && Array.isArray(msg.content)) {
              for (const block of msg.content) {
                if (block.type === 'text') {
                  const text = block.text;
                  console.log('📝 Text segment found:', text.substring(0, 50) + '...');
                  this.currentMessageResponse += text;
                  
                  // Send as discrete text segment (not chunk)
                  if (this.currentOnChunk) {
                    this.currentOnChunk(text);
                  }
                  console.log('📤 Text segment sent to UI');
                } else if (block.type === 'tool_use') {
                  console.log(`\n🔧 ${block.name}:`, JSON.stringify(block.input, null, 2));
                  // Capture and stream tool usage immediately
                  const toolUse = {
                    name: block.name,
                    input: block.input
                  };
                  this.currentToolUses.push(toolUse);
                  
                  // Stream tool use immediately if callback provided
                  if (this.currentOnToolUse) {
                    this.currentOnToolUse(toolUse);
                  }
                }
              }
            }
            // Handle direct text format
            else if (msg.content && typeof msg.content === 'string') {
              console.log('📝 Direct text segment found:', msg.content.substring(0, 50) + '...');
              this.currentMessageResponse += msg.content;
              
              // Send as discrete text segment (not chunk)
              if (this.currentOnChunk) {
                this.currentOnChunk(msg.content);
              }
              console.log('📤 Text segment sent to UI');
            }
          } else if (msg.type === 'user') {
            // Handle tool results
            if (msg.content && Array.isArray(msg.content)) {
              for (const block of msg.content) {
                if (block.type === 'tool_result') {
                  console.log('🔧 Tool result received:', {
                    content: block.content.substring(0, 100) + '...',
                    isError: block.is_error
                  });
                  
                  // Stream tool result immediately if callback provided
                  if (this.currentOnToolResult) {
                    this.currentOnToolResult({
                      content: block.content,
                      isError: block.is_error
                    });
                  }
                }
              }
            }
          } else if (msg.type === 'result' && msg.subtype === 'success') {
            // This indicates the message is complete
            console.log('🏁 Message completed with result');
            this.currentMessageResolved = true;
          } else {
            console.log('⚠️ Non-assistant message type:', msg.type);
          }
        });

        console.log('✅ Persistent conversation and stream handler created');
      }

      // Send the message using the persistent conversation
      console.log('📤 Sending message to persistent conversation...');
      await this.conversation.send(message);

      

      // Wait for the message to complete (wait for result message)
      console.log('⏳ Waiting for message completion...');
      while (!this.currentMessageResolved) {
        await new Promise(resolve => setTimeout(resolve, 10));
      }

      console.log('🌊 Streaming completed, total chunks for this message:', this.currentChunkCount);
      console.log('🌊 Streaming completed, this message length:', this.currentMessageResponse.length);
      console.log('🔧 Tool uses captured:', this.currentToolUses.length);
      console.log('🏁 Final sessionId:', this.sessionId);

      return {
        response: this.currentMessageResponse,
        toolUses: this.currentToolUses
      };
    } catch (error) {
      console.error('❌ Claude streaming error:', error);
      throw new Error('Failed to get Claude streaming response');
    }
  }

  // Set session ID for resuming loaded sessions
  setSessionId(sessionId: string): void {
    console.log('🔄 Setting session ID for loaded session:', sessionId);
    this.sessionId = sessionId;
    this.isLoadedSession = true; // Mark as loaded session
    console.log('✅ Session ID set for continuation, isLoadedSession:', this.isLoadedSession);
  }

  // Clear current session to start fresh
  async clearSession(): Promise<void> {
    console.log('🧹 Clearing current session');

    // End and dispose persistent conversation if it exists
    if (this.conversation) {
      console.log('🔚 Ending persistent conversation');
      try {
        await this.conversation.end();
        // Small delay to let any pending SDK timeouts complete before disposing
        // This prevents race condition where dispose() clears activeClient while
        // end()'s timeout callbacks are still pending
        console.log('⏳ Waiting for SDK cleanup timeouts to complete...');
        await new Promise(resolve => setTimeout(resolve, 150));
        this.conversation.dispose();
        console.log('✅ Persistent conversation ended and disposed');
      } catch (error) {
        console.warn('⚠️ Error ending conversation:', error);
      }
      this.conversation = null;
    }

    // Reset all state
    this.sessionId = null;
    this.isLoadedSession = false; // Reset loaded session flag
    this.currentDbSessionId = null; // Reset session tracking
    this.forkedSessionId = null; // Reset forked session ID
    this.builder = claude();
    this.currentMessageResponse = '';
    this.currentMessageResolved = false;
    this.currentChunkCount = 0;
    this.currentOnChunk = null;
    this.currentOnToolUse = null;
    this.currentOnToolResult = null;
    this.currentToolUses = [];

    console.log('✅ Session cleared - next message will start new persistent conversation');
  }

  // For future multi-chat support
  async createNewSession(): Promise<void> {
    console.log('🔄 Creating new session - resetting sessionId');

    // Use the improved clearSession method that handles race conditions
    await this.clearSession();
    this.isLoadedSession = false; // Reset loaded session flag
    this.currentDbSessionId = null; // Reset session tracking
    this.forkedSessionId = null; // Reset forked session ID
    this.currentMessageResponse = '';
    this.currentMessageResolved = false;
    this.currentChunkCount = 0;
    this.currentOnChunk = null;
    this.currentOnToolUse = null;
    this.currentOnToolResult = null;
    this.currentToolUses = [];

    console.log('✅ Session reset complete');
  }
}