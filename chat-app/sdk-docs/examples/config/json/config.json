{"project": {"name": "claude-code-sdk-examples", "version": "1.0.0", "description": "Example configurations for Claude Code SDK"}, "sdk": {"timeout": 30000, "maxRetries": 3, "streamMessages": true}, "claude": {"model": "claude-opus-4-20250514", "temperature": 0.7, "maxTokens": 4096}, "logging": {"level": "info", "format": "json", "outputPath": "./logs"}, "development": {"debug": false, "verboseErrors": true, "mockMode": false}}