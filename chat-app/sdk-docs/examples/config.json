{"project": {"name": "claude-code-sdk-examples", "version": "1.0.0", "description": "Example configurations for Claude Code SDK"}, "sdk": {"apiVersion": "v1", "timeout": 30000, "maxRetries": 3, "streamingEnabled": true}, "claude": {"model": "claude-3-sonnet-20240229", "maxTokens": 4096, "temperature": 0.7, "systemPrompt": "You are a helpful assistant"}, "logging": {"level": "info", "format": "json", "enabled": true}, "development": {"debug": false, "verbose": false, "mockMode": false}}