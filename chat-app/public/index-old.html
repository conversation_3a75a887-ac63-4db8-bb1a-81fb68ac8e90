<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON> Chat</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdn.jsdelivr.net/npm/daisyui@4.4.19/dist/full.min.css" rel="stylesheet" type="text/css" />
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        'mono': ['Monaco', 'Menlo', 'Consolas', monospace]
                    }
                }
            }
        }
    </script>
    <style>
        /* Custom styles for code content */
        .code-content {
            font-family: 'Monaco', '<PERSON><PERSON>', 'Consolas', monospace;
            font-size: 12px;
            line-height: 1.4;
            white-space: pre-wrap;
            overflow-x: auto;
        }
        
        /* Chat message styling */
        .chat-message {
            margin: 8px 0;
        }
        
        /* Tool usage and result cards */
        .tool-card {
            margin: 8px 0;
        }
        .tool-card .card-title {
           color: white;
        }
    </style>
</head>
<body class="bg-base-100 min-h-screen">
    <div class="container mx-auto p-4 max-w-4xl">
        <!-- Header -->
        <div class="text-center mb-6">
            <h1 class="text-3xl font-bold text-primary">Claude Voice Chat</h1>
        </div>
        
        <!-- Session Management Card -->
        <div class="card bg-base-200 shadow-xl mb-6">
            <div class="card-body">
                <h2 class="card-title text-lg">
                    <i data-lucide="settings" class="w-5 h-5 mr-2"></i>
                    Session Management
                </h2>
                <div class="flex flex-col sm:flex-row gap-2 mb-4">
                    <input type="text" 
                           id="projectInput" 
                           placeholder="Project (e.g., -Users-jaypears-Documents-claude-chat)" 
                           class="input input-bordered flex-1">
                    <input type="text" 
                           id="sessionInput" 
                           placeholder="Session ID (e.g., c513468f-9afb...)" 
                           class="input input-bordered flex-1">
                    <button onclick="loadSession()" class="btn btn-primary">
                        <i data-lucide="download" class="w-4 h-4 mr-2"></i>
                        Load
                    </button>
                    <button onclick="clearSession()" class="btn btn-error">
                        <i data-lucide="trash-2" class="w-4 h-4 mr-2"></i>
                        Clear
                    </button>
                </div>
                <div class="alert alert-info">
                    <i data-lucide="activity" class="w-4 h-4"></i>
                    <div>
                        <strong>Current Session ID:</strong> 
                        <span id="currentSessionId" class="font-mono text-sm">None</span>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Chat Messages -->
        <div class="card bg-base-100 shadow-xl mb-4">
            <div class="card-body p-4">
                <div id="messages" class="h-96 overflow-y-auto border border-base-300 rounded-lg p-4 bg-base-50"></div>
            </div>
        </div>
        
        <!-- Input Area -->
        <div class="card bg-base-200 shadow-xl">
            <div class="card-body">
                <div class="flex flex-col sm:flex-row gap-2">
                    <input type="text" 
                           id="messageInput" 
                           placeholder="Type your message..." 
                           class="input input-bordered flex-1">
                    <button onclick="sendMessage()" class="btn btn-primary">
                        <i data-lucide="send" class="w-4 h-4 mr-2"></i>
                        Send
                    </button>
                    <button onclick="toggleVoice()" id="voiceBtn" class="btn btn-secondary">
                        <i data-lucide="mic-off" class="w-4 h-4 mr-2"></i>
                        Voice: OFF
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
    <script src="app.js"></script>
</body>
</html>