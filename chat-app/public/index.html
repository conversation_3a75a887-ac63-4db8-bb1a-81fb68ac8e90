<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="theme-color" content="#6366f1">
    <title><PERSON> Voice Chat</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdn.jsdelivr.net/npm/daisyui@4.4.19/dist/full.min.css" rel="stylesheet" type="text/css" />
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        'mono': ['Monaco', 'Menlo', 'Consolas', monospace]
                    }
                }
            }
        }
    </script>
    <style>
        /* CSS Custom Properties for Dynamic Viewport Heights */
        :root {
            --app-height: 100%;
            --chat-input-height: 80px;
            --session-header-height: 80px;
            
            /* Modern Color Palette */
            --color-primary: #6366f1;
            --color-primary-dark: #4f46e5;
            --color-primary-light: #818cf8;
            --color-secondary: #8b5cf6;
            --color-accent: #06b6d4;
            --color-neutral: #1f2937;
            --color-neutral-light: #374151;
            --color-neutral-lighter: #6b7280;
            --color-base-100: #ffffff;
            --color-base-200: #f8fafc;
            --color-base-300: #e2e8f0;
            --color-base-content: #1e293b;
            --color-success: #10b981;
            --color-warning: #f59e0b;
            --color-error: #ef4444;
            --color-info: #3b82f6;
            
            /* Dark theme variants */
            --color-dark-base-100: #0f172a;
            --color-dark-base-200: #1e293b;
            --color-dark-base-300: #334155;
            --color-dark-content: #f1f5f9;
        }

        /* Dynamic viewport height calculation */
        .app-height {
            height: 100vh; /* Fallback */
            height: var(--app-height);
        }
        
        /* Chat layout - Mobile-first design */
        .chat-container {
            display: flex;
            flex-direction: column;
            height: 100vh;
            height: var(--app-height);
            position: relative;
        }
        
        .chat-messages-area {
            flex: 1;
            overflow-y: auto;
            overscroll-behavior-y: contain;
            -webkit-overflow-scrolling: touch;
            padding-bottom: calc(var(--chat-input-height) + 100px);
            scroll-behavior: smooth;
        }
        
        .chat-input-area {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: var(--chat-input-height);
            background: var(--color-base-100);
            border-top: 1px solid var(--color-base-300);
            z-index: 30;
            padding: 1rem;
            box-shadow: 0 -4px 6px -1px rgba(0, 0, 0, 0.1);
        }
        
        /* Adjust for sidebar on desktop */
        @media (min-width: 769px) {
            .chat-input-area {
                left: 320px; /* Sidebar width */
            }
        }
        
        /* Input area content */
        .chat-input-content {
            max-width: 64rem;
            margin: 0 auto;
            display: flex;
            gap: 0.5rem;
            align-items: center;
        }
        
        .chat-input {
            flex: 1;
            min-height: 48px;
            border-radius: 24px;
            border: 2px solid var(--color-base-300);
            padding: 0.75rem 1.25rem;
            font-size: 16px;
            background: var(--color-base-100);
            transition: all 0.2s ease;
        }
        
        .chat-input:focus {
            outline: none;
            border-color: var(--color-primary);
            box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
        }
        
        .chat-send-btn {
            min-width: 48px;
            min-height: 48px;
            border-radius: 50%;
            background: var(--color-primary);
            color: white;
            border: none;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.2s ease;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        
        .chat-send-btn:hover {
            background: var(--color-primary-dark);
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
        }
        
        .chat-send-btn:disabled {
            background: var(--color-neutral-lighter);
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        /* Custom styles for code content */
        .code-content {
            font-family: 'Monaco', 'Menlo', 'Consolas', monospace;
            font-size: 14px;
            line-height: 1.5;
            white-space: pre-wrap;
            overflow-x: auto;
        }
        
        /* Mobile responsive code content */
        @media (max-width: 768px) {
            .code-content {
                font-size: 12px;
                line-height: 1.4;
            }
        }
        
        /* Chat message styling */
        .chat-message {
            margin: 12px 0;
        }
        
        /* Tool usage and result cards */
        .tool-card {
            margin: 12px 0;
        }
        .tool-card .card-title {
           color: var(--color-base-content);
        }

        /* Sidebar positioning */
        .sidebar {
            height: 100vh;
            height: var(--app-height);
            overflow-y: auto;
            transition: transform 0.3s ease-in-out;
            background: var(--color-base-200);
            border-right: 1px solid var(--color-base-300);
        }
        
        /* Main content area */
        .main-content {
            height: 100vh;
            height: var(--app-height);
            overflow: hidden;
            display: flex;
            flex-direction: column;
        }
        
        /* Mobile sidebar overlay */
        @media (max-width: 768px) {
            .sidebar {
                position: fixed;
                top: 0;
                left: 0;
                z-index: 50;
                transform: translateX(-100%);
                width: 280px;
                background: var(--color-base-100);
                backdrop-filter: blur(20px);
                border-right: 1px solid var(--color-base-300);
                box-shadow: 4px 0 16px rgba(0, 0, 0, 0.1);
            }
            
            .sidebar.open {
                transform: translateX(0);
            }
            
            .sidebar-overlay {
                position: fixed;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: rgba(15, 23, 42, 0.6);
                z-index: 40;
                opacity: 0;
                visibility: hidden;
                transition: opacity 0.3s ease-in-out, visibility 0.3s ease-in-out;
                backdrop-filter: blur(4px);
            }
            
            .sidebar-overlay.active {
                opacity: 1;
                visibility: visible;
            }
            
            .main-content {
                width: 100%;
            }
            
            .chat-input-area {
                left: 0;
                padding: 1rem;
                padding-bottom: max(1rem, env(safe-area-inset-bottom));
            }
        }
        
        /* Project accordion custom styling */
        .project-accordion .collapse-title {
            min-height: 3rem;
        }
        
        /* Session item hover effect */
        .session-item:hover {
            background: var(--color-base-300);
            transition: all 0.2s ease;
            transform: translateX(2px);
        }
        
        /* Active session highlight */
        .session-item.active {
            background: linear-gradient(135deg, rgba(99, 102, 241, 0.1), rgba(139, 92, 246, 0.1));
            border-left: 3px solid var(--color-primary);
            box-shadow: 0 2px 8px rgba(99, 102, 241, 0.15);
        }
        
        /* Mobile hamburger menu */
        .mobile-menu-btn {
            display: none;
        }
        
        @media (max-width: 768px) {
            .mobile-menu-btn {
                display: flex;
                position: fixed;
                top: 1rem;
                left: 1rem;
                z-index: 60;
                min-height: 48px;
                min-width: 48px;
                background: var(--color-primary);
                border: none;
                border-radius: 12px;
                color: white;
                box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3);
                transition: all 0.2s ease;
            }
            
            .mobile-menu-btn:hover {
                background: var(--color-primary-dark);
                transform: translateY(-1px);
                box-shadow: 0 6px 16px rgba(99, 102, 241, 0.4);
            }
        }
        
        /* Touch-friendly interactive elements */
        @media (max-width: 768px) {
            .btn {
                min-height: 48px;
                min-width: 48px;
                padding: 0.75rem 1rem;
                border-radius: 12px;
                font-weight: 500;
                transition: all 0.2s ease;
            }
            
            .btn-sm {
                min-height: 44px;
                min-width: 44px;
                padding: 0.5rem 0.75rem;
                border-radius: 10px;
            }
            
            .btn-xs {
                min-height: 40px;
                min-width: 40px;
                padding: 0.375rem 0.5rem;
                border-radius: 8px;
            }
            
            .input {
                min-height: 48px;
                font-size: 16px;
                padding: 0.75rem 1rem;
                border-radius: 12px;
                border: 2px solid var(--color-base-300);
                transition: all 0.2s ease;
            }
            
            .input:focus {
                border-color: var(--color-primary);
                box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
            }
            
            .session-item {
                min-height: 64px;
                padding: 1rem;
                border-radius: 12px;
                margin: 0.5rem 0;
                transition: all 0.2s ease;
            }
            
            .collapse-title {
                min-height: 64px;
                padding: 1rem;
                border-radius: 12px;
                transition: all 0.2s ease;
            }
        }
        
        /* Mobile typography improvements */
        @media (max-width: 768px) {
            body {
                font-size: 16px;
                line-height: 1.5;
            }
            
            .text-xs {
                font-size: 14px;
            }
            
            .text-sm {
                font-size: 16px;
            }
            
            .chat-bubble {
                font-size: 16px;
                line-height: 1.5;
                padding: 1rem;
            }
        }
        
        /* Mobile spacing improvements */
        @media (max-width: 768px) {
            .space-y-4 > * + * {
                margin-top: 1.5rem;
            }
            
            .p-4 {
                padding: 1.5rem;
            }
            
            .px-4 {
                padding-left: 1.5rem;
                padding-right: 1.5rem;
            }
            
            .py-4 {
                padding-top: 1.5rem;
                padding-bottom: 1.5rem;
            }
            
            /* Mobile modal improvements */
            .modal-box {
                margin: 1rem;
                max-height: calc(100vh - 2rem);
                overflow-y: auto;
            }
            
            /* Mobile message area improvements */
            .max-w-4xl {
                max-width: 100%;
                padding: 0 1rem;
            }
            
            /* Mobile welcome screen */
            .text-4xl {
                font-size: 2rem;
                line-height: 2.5rem;
            }
            
            /* Mobile card improvements */
            .card-body {
                padding: 1rem;
            }
            
            /* Mobile mockup code improvements */
            .mockup-code {
                font-size: 12px;
                overflow-x: auto;
                max-width: 100%;
            }
            
            /* Hide desktop-only elements */
            .desktop-only {
                display: none;
            }
            
            /* Show mobile-only elements */
            .mobile-only {
                display: block;
            }
        }
        
        /* Desktop-only styles */
        @media (min-width: 769px) {
            .mobile-only {
                display: none;
            }
            
            .desktop-only {
                display: block;
            }
        }
        
        /* Safe area handling for modern mobile devices */
        @supports (padding: max(0px)) {
            .main-content {
                padding-left: max(0px, env(safe-area-inset-left));
                padding-right: max(0px, env(safe-area-inset-right));
            }
            
            .sidebar {
                padding-left: max(1rem, env(safe-area-inset-left));
            }
            
            @media (max-width: 768px) {
                .mobile-menu-btn {
                    left: max(1rem, env(safe-area-inset-left));
                    top: max(1rem, env(safe-area-inset-top));
                }
            }
        }
        
        /* Improve scrolling on mobile */
        @media (max-width: 768px) {
            * {
                -webkit-overflow-scrolling: touch;
            }
            
            .overflow-y-auto {
                overscroll-behavior-y: contain;
            }
        }
        
        /* Modern button styles */
        .btn-primary {
            background: linear-gradient(135deg, var(--color-primary), var(--color-primary-dark));
            border: none;
            color: white;
            font-weight: 600;
            box-shadow: 0 2px 8px rgba(99, 102, 241, 0.3);
            transition: all 0.2s ease;
        }
        
        .btn-primary:hover {
            background: linear-gradient(135deg, var(--color-primary-dark), var(--color-primary));
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(99, 102, 241, 0.4);
        }
        
        .btn-secondary {
            background: linear-gradient(135deg, var(--color-secondary), #a855f7);
            border: none;
            color: white;
            font-weight: 600;
            box-shadow: 0 2px 8px rgba(139, 92, 246, 0.3);
        }
        
        .btn-secondary:hover {
            background: linear-gradient(135deg, #7c3aed, var(--color-secondary));
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(139, 92, 246, 0.4);
        }
        
        /* Modern card styles */
        .card {
            border-radius: 16px;
            border: 1px solid var(--color-base-300);
            background: var(--color-base-100);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
            transition: all 0.2s ease;
        }
        
        .card:hover {
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
            transform: translateY(-1px);
        }
        
        /* Modern chat bubbles */
        .chat-bubble {
            border-radius: 18px;
            padding: 1rem 1.25rem;
            font-size: 15px;
            line-height: 1.5;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
        }
        
        .chat-bubble-primary {
            background: linear-gradient(135deg, var(--color-primary), var(--color-primary-dark));
            color: white;
        }
        
        .chat-bubble-secondary {
            background: linear-gradient(135deg, var(--color-secondary), #a855f7);
            color: white;
        }
        
        /* Welcome screen styling */
        .welcome-container {
            background: linear-gradient(135deg, var(--color-base-100), var(--color-base-200));
            min-height: 100vh;
            min-height: var(--app-height);
        }
        
        /* Session header styling */
        .session-header {
            background: var(--color-base-100);
            border-bottom: 1px solid var(--color-base-300);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
            height: var(--session-header-height);
            display: flex;
            align-items: center;
        }
    </style>
    <script>
        // Set CSS custom property for dynamic viewport height
        function setAppHeight() {
            const doc = document.documentElement;
            doc.style.setProperty('--app-height', (window.innerHeight)+'px');
        }
        
        window.addEventListener('resize', setAppHeight);
        window.addEventListener('orientationchange', () => {
            setTimeout(setAppHeight, 100);
        });
        setAppHeight();
    </script>
</head>
<body style="background: var(--color-base-100); color: var(--color-base-content);">
    <!-- Mobile Menu Button -->
    <button id="mobileMenuBtn" class="mobile-menu-btn" onclick="toggleMobileSidebar()">
        <i data-lucide="menu" class="w-5 h-5"></i>
    </button>
    
    <!-- Sidebar Overlay for Mobile -->
    <div id="sidebarOverlay" class="sidebar-overlay" onclick="closeMobileSidebar()"></div>
    
    <!-- Main Layout: Sidebar + Content -->
    <div class="flex h-screen">
        
        <!-- Left Sidebar: Projects -->
        <div id="sidebar" class="sidebar w-80 md:w-80 p-4">
            <div class="flex items-center justify-between mb-6">
                <h2 class="text-xl font-bold text-base-content">
                    <i data-lucide="folder" class="w-5 h-5 inline mr-2"></i>
                    Projects
                </h2>
                <button onclick="openNewProjectModal()" class="btn btn-primary btn-sm">
                    <i data-lucide="plus" class="w-4 h-4"></i>
                </button>
            </div>
            
            <!-- Projects List -->
            <div id="projectsList" class="space-y-2">
                <!-- Projects will be populated here -->
                <div class="text-center text-base-content/50 py-8">
                    <i data-lucide="folder-plus" class="w-12 h-12 mx-auto mb-2 opacity-50"></i>
                    <p>No projects yet</p>
                    <p class="text-sm">Click + to create one</p>
                </div>
            </div>
        </div>
        
        <!-- Main Content: Chat Interface -->
        <div class="main-content flex-1 flex flex-col">
            
            <!-- Session Header -->
            <div id="sessionHeader" class="session-header p-4" style="display: none;">
                <div class="flex items-center justify-between w-full max-w-4xl mx-auto">
                    <div>
                        <h1 class="text-2xl font-bold" id="sessionTitle" style="color: var(--color-primary);">Select a Session</h1>
                        <p class="text-sm" id="sessionInfo" style="color: var(--color-neutral-lighter);">No active session</p>
                    </div>
                    <button onclick="toggleVoice()" id="voiceBtn" class="btn btn-secondary btn-sm">
                        <i data-lucide="mic-off" class="w-4 h-4 mr-2"></i>
                        Voice: OFF
                    </button>
                </div>
            </div>
            
            <!-- Welcome Screen (shown when no session selected) -->
            <div id="welcomeScreen" class="welcome-container flex-1 flex items-center justify-center">
                <div class="text-center">
                    <div class="w-24 h-24 mx-auto mb-6 rounded-full flex items-center justify-center" style="background: linear-gradient(135deg, var(--color-primary), var(--color-secondary)); box-shadow: 0 8px 24px rgba(99, 102, 241, 0.3);">
                        <i data-lucide="message-circle" class="w-12 h-12 text-white"></i>
                    </div>
                    <h1 class="text-4xl font-bold mb-3" style="background: linear-gradient(135deg, var(--color-primary), var(--color-secondary)); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text;">Claude Voice Chat</h1>
                    <p class="text-lg mb-8" style="color: var(--color-neutral-lighter);">Create a project and session to get started</p>
                    <button onclick="openNewProjectModal()" class="btn btn-primary" style="padding: 1rem 2rem; font-size: 1.1rem; border-radius: 16px;">
                        <i data-lucide="plus" class="w-5 h-5 mr-2"></i>
                        Create Your First Project
                    </button>
                </div>
            </div>
            
            <!-- Chat Interface (hidden initially) -->
            <div id="chatInterface" class="chat-container" style="display: none;">
                
                <!-- Chat Messages -->
                <div class="chat-messages-area">
                    <div class="p-4">
                        <div id="messages" class="space-y-4 max-w-4xl mx-auto">
                            <!-- Messages will be populated here -->
                        </div>
                    </div>
                </div>
                
            </div>
            
        </div>
        
    </div>
    
    <!-- Fixed Input Area (appears when chat is active) -->
    <div id="chatInputArea" class="chat-input-area" style="display: none;">
        <div class="chat-input-content">
            <input type="text" 
                   id="messageInput" 
                   placeholder="Type your message..." 
                   class="chat-input"
                   disabled>
            <button onclick="sendMessage()" id="sendBtn" class="chat-send-btn" disabled>
                <i data-lucide="send" class="w-5 h-5"></i>
            </button>
        </div>
    </div>
    
    <!-- New Project Modal -->
    <dialog id="newProjectModal" class="modal">
        <div class="modal-box max-w-lg w-full mx-4" style="background: var(--color-base-100); border-radius: 20px; border: 1px solid var(--color-base-300); box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);">
            <h3 class="font-bold text-lg mb-6 flex items-center" style="color: var(--color-base-content);">
                <div class="w-8 h-8 rounded-lg flex items-center justify-center mr-3" style="background: linear-gradient(135deg, var(--color-primary), var(--color-secondary)); box-shadow: 0 2px 8px rgba(99, 102, 241, 0.3);">
                    <i data-lucide="folder-plus" class="w-4 h-4 text-white"></i>
                </div>
                Create New Project
            </h3>
            
            <div class="form-control mb-4">
                <label class="label">
                    <span class="label-text">Project Path</span>
                </label>
                <div class="flex gap-2">
                    <input type="text" 
                           id="projectPathInput" 
                           placeholder="/Users/<USER>/Documents/my-project" 
                           class="input input-bordered flex-1"
                           autocomplete="off"
                           autocorrect="off"
                           autocapitalize="none"
                           spellcheck="false">
                    <button class="btn btn-outline btn-square" title="Browse folders (coming soon)" disabled>
                        <i data-lucide="folder" class="w-4 h-4"></i>
                    </button>
                </div>
                <label class="label">
                    <span class="label-text-alt text-info">
                        <i data-lucide="info" class="w-3 h-3 inline mr-1"></i>
                        Path must exist and be a valid directory
                    </span>
                </label>
            </div>
            
            <div class="form-control mb-6">
                <label class="label">
                    <span class="label-text">Project Name</span>
                </label>
                <input type="text" 
                       id="projectNameInput" 
                       placeholder="Auto-filled from path" 
                       class="input input-bordered">
                <label class="label">
                    <span class="label-text-alt">Leave empty to use folder name</span>
                </label>
            </div>
            
            <div class="modal-action">
                <button onclick="closeNewProjectModal()" class="btn btn-outline">Cancel</button>
                <button onclick="createProject()" class="btn btn-primary">
                    <i data-lucide="plus" class="w-4 h-4 mr-2"></i>
                    Create Project
                </button>
            </div>
        </div>
        <form method="dialog" class="modal-backdrop">
            <button onclick="closeNewProjectModal()">close</button>
        </form>
    </dialog>
    
    <!-- New Session Modal -->
    <dialog id="newSessionModal" class="modal">
        <div class="modal-box max-w-lg w-full mx-4" style="background: var(--color-base-100); border-radius: 20px; border: 1px solid var(--color-base-300); box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);">
            <h3 class="font-bold text-lg mb-6 flex items-center" style="color: var(--color-base-content);">
                <div class="w-8 h-8 rounded-lg flex items-center justify-center mr-3" style="background: linear-gradient(135deg, var(--color-secondary), var(--color-accent)); box-shadow: 0 2px 8px rgba(139, 92, 246, 0.3);">
                    <i data-lucide="plus-circle" class="w-4 h-4 text-white"></i>
                </div>
                Create New Session
            </h3>
            
            <div class="form-control mb-6">
                <label class="label">
                    <span class="label-text">Session Name</span>
                </label>
                <input type="text" 
                       id="sessionNameInput" 
                       placeholder="e.g., Feature Development, Bug Fixes, etc." 
                       class="input input-bordered"
                       maxlength="50">
                <label class="label">
                    <span class="label-text-alt">1-50 characters, letters, numbers, spaces, hyphens, underscores</span>
                </label>
            </div>
            
            <div class="modal-action">
                <button onclick="closeNewSessionModal()" class="btn btn-outline">Cancel</button>
                <button onclick="createSession()" class="btn btn-primary">
                    <i data-lucide="plus" class="w-4 h-4 mr-2"></i>
                    Create Session
                </button>
            </div>
        </div>
        <form method="dialog" class="modal-backdrop">
            <button onclick="closeNewSessionModal()">close</button>
        </form>
    </dialog>
    
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
    <script src="app.js"></script>
</body>
</html>