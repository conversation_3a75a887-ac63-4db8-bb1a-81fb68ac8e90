<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON>t</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdn.jsdelivr.net/npm/daisyui@4.4.19/dist/full.min.css" rel="stylesheet" type="text/css" />
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        'mono': ['Monaco', 'Menlo', 'Consolas', monospace]
                    }
                }
            }
        }
    </script>
    <style>
        /* Custom styles for code content */
        .code-content {
            font-family: 'Monaco', '<PERSON><PERSON>', 'Consolas', monospace;
            font-size: 12px;
            line-height: 1.4;
            white-space: pre-wrap;
            overflow-x: auto;
        }
        
        /* Chat message styling */
        .chat-message {
            margin: 8px 0;
        }
        
        /* Tool usage and result cards */
        .tool-card {
            margin: 8px 0;
        }
        .tool-card .card-title {
           color: white;
        }

        /* Sidebar fixed positioning */
        .sidebar {
            height: 100vh;
            overflow-y: auto;
        }
        
        /* Main content area */
        .main-content {
            height: 100vh;
            overflow: hidden;
        }
        
        /* Project accordion custom styling */
        .project-accordion .collapse-title {
            min-height: 3rem;
        }
        
        /* Session item hover effect */
        .session-item:hover {
            background-color: rgba(0,0,0,0.1);
        }
        
        /* Active session highlight */
        .session-item.active {
            background-color: rgba(59, 130, 246, 0.1);
            border-left: 3px solid rgb(59, 130, 246);
        }
    </style>
</head>
<body class="bg-base-100">
    <!-- Main Layout: Sidebar + Content -->
    <div class="flex h-screen">
        
        <!-- Left Sidebar: Projects -->
        <div class="sidebar w-80 bg-base-200 border-r border-base-300 p-4">
            <div class="flex items-center justify-between mb-6">
                <h2 class="text-xl font-bold text-base-content">
                    <i data-lucide="folder" class="w-5 h-5 inline mr-2"></i>
                    Projects
                </h2>
                <button onclick="openNewProjectModal()" class="btn btn-primary btn-sm">
                    <i data-lucide="plus" class="w-4 h-4"></i>
                </button>
            </div>
            
            <!-- Projects List -->
            <div id="projectsList" class="space-y-2">
                <!-- Projects will be populated here -->
                <div class="text-center text-base-content/50 py-8">
                    <i data-lucide="folder-plus" class="w-12 h-12 mx-auto mb-2 opacity-50"></i>
                    <p>No projects yet</p>
                    <p class="text-sm">Click + to create one</p>
                </div>
            </div>
        </div>
        
        <!-- Main Content: Chat Interface -->
        <div class="main-content flex-1 flex flex-col">
            
            <!-- Session Header -->
            <div id="sessionHeader" class="bg-base-100 border-b border-base-300 p-4" style="display: none;">
                <div class="flex items-center justify-between">
                    <div>
                        <h1 class="text-2xl font-bold text-primary" id="sessionTitle">Select a Session</h1>
                        <p class="text-sm text-base-content/70" id="sessionInfo">No active session</p>
                    </div>
                    <button onclick="toggleVoice()" id="voiceBtn" class="btn btn-secondary btn-sm">
                        <i data-lucide="mic-off" class="w-4 h-4 mr-2"></i>
                        Voice: OFF
                    </button>
                </div>
            </div>
            
            <!-- Welcome Screen (shown when no session selected) -->
            <div id="welcomeScreen" class="flex-1 flex items-center justify-center bg-base-50">
                <div class="text-center">
                    <i data-lucide="message-circle" class="w-24 h-24 mx-auto mb-4 text-primary/30"></i>
                    <h1 class="text-4xl font-bold text-primary mb-2">Claude Voice Chat</h1>
                    <p class="text-lg text-base-content/70 mb-6">Create a project and session to get started</p>
                    <button onclick="openNewProjectModal()" class="btn btn-primary btn-lg">
                        <i data-lucide="plus" class="w-5 h-5 mr-2"></i>
                        Create Your First Project
                    </button>
                </div>
            </div>
            
            <!-- Chat Interface (hidden initially) -->
            <div id="chatInterface" class="flex-1 flex flex-col" style="display: none;">
                
                <!-- Chat Messages -->
                <div class="flex-1 p-4 overflow-y-auto">
                    <div id="messages" class="space-y-4 max-w-4xl mx-auto">
                        <!-- Messages will be populated here -->
                    </div>
                </div>
                
                <!-- Input Area -->
                <div class="bg-base-200 border-t border-base-300 p-4">
                    <div class="max-w-4xl mx-auto">
                        <div class="flex gap-2">
                            <input type="text" 
                                   id="messageInput" 
                                   placeholder="Type your message..." 
                                   class="input input-bordered flex-1"
                                   disabled>
                            <button onclick="sendMessage()" id="sendBtn" class="btn btn-primary" disabled>
                                <i data-lucide="send" class="w-4 h-4 mr-2"></i>
                                Send
                            </button>
                        </div>
                    </div>
                </div>
                
            </div>
            
        </div>
        
    </div>
    
    <!-- New Project Modal -->
    <dialog id="newProjectModal" class="modal">
        <div class="modal-box">
            <h3 class="font-bold text-lg mb-4">
                <i data-lucide="folder-plus" class="w-5 h-5 inline mr-2"></i>
                Create New Project
            </h3>
            
            <div class="form-control mb-4">
                <label class="label">
                    <span class="label-text">Project Path</span>
                </label>
                <div class="flex gap-2">
                    <input type="text" 
                           id="projectPathInput" 
                           placeholder="/Users/<USER>/Documents/my-project" 
                           class="input input-bordered flex-1">
                    <button class="btn btn-outline btn-square" title="Browse folders (coming soon)" disabled>
                        <i data-lucide="folder" class="w-4 h-4"></i>
                    </button>
                </div>
                <label class="label">
                    <span class="label-text-alt text-info">
                        <i data-lucide="info" class="w-3 h-3 inline mr-1"></i>
                        Path must exist and be a valid directory
                    </span>
                </label>
            </div>
            
            <div class="form-control mb-6">
                <label class="label">
                    <span class="label-text">Project Name</span>
                </label>
                <input type="text" 
                       id="projectNameInput" 
                       placeholder="Auto-filled from path" 
                       class="input input-bordered">
                <label class="label">
                    <span class="label-text-alt">Leave empty to use folder name</span>
                </label>
            </div>
            
            <div class="modal-action">
                <button onclick="closeNewProjectModal()" class="btn btn-outline">Cancel</button>
                <button onclick="createProject()" class="btn btn-primary">
                    <i data-lucide="plus" class="w-4 h-4 mr-2"></i>
                    Create Project
                </button>
            </div>
        </div>
        <form method="dialog" class="modal-backdrop">
            <button onclick="closeNewProjectModal()">close</button>
        </form>
    </dialog>
    
    <!-- New Session Modal -->
    <dialog id="newSessionModal" class="modal">
        <div class="modal-box">
            <h3 class="font-bold text-lg mb-4">
                <i data-lucide="plus-circle" class="w-5 h-5 inline mr-2"></i>
                Create New Session
            </h3>
            
            <div class="form-control mb-6">
                <label class="label">
                    <span class="label-text">Session Name</span>
                </label>
                <input type="text" 
                       id="sessionNameInput" 
                       placeholder="e.g., Feature Development, Bug Fixes, etc." 
                       class="input input-bordered"
                       maxlength="50">
                <label class="label">
                    <span class="label-text-alt">1-50 characters, letters, numbers, spaces, hyphens, underscores</span>
                </label>
            </div>
            
            <div class="modal-action">
                <button onclick="closeNewSessionModal()" class="btn btn-outline">Cancel</button>
                <button onclick="createSession()" class="btn btn-primary">
                    <i data-lucide="plus" class="w-4 h-4 mr-2"></i>
                    Create Session
                </button>
            </div>
        </div>
        <form method="dialog" class="modal-backdrop">
            <button onclick="closeNewSessionModal()">close</button>
        </form>
    </dialog>
    
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
    <script src="app.js"></script>
</body>
</html>