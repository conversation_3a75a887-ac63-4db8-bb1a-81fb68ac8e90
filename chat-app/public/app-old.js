let voiceEnabled = false;

function updateSessionDisplay(sessionId) {
    const display = document.getElementById('currentSessionId');
    if (sessionId) {
        display.textContent = sessionId;
        display.style.color = '#333';
    } else {
        display.textContent = 'None';
        display.style.color = '#666';
    }
}

async function sendMessage() {
    const input = document.getElementById('messageInput');
    const message = input.value.trim();
    if (!message) return;

    addMessage('You', message, 'user');
    input.value = '';

    // Always use streaming now
    await sendMessageStreaming(message);
}

// TTS Queue management
let ttsQueue = [];
let isTTSPlaying = false;

async function processNextTTS() {
    if (isTTSPlaying || ttsQueue.length === 0) return;
    
    isTTSPlaying = true;
    const text = ttsQueue.shift();
    
    try {
        console.log('🔊 Playing TTS for segment:', text.substring(0, 50) + '...');
        await speakText(text);
    } catch (error) {
        console.error('❌ TTS error:', error);
    } finally {
        isTTSPlaying = false;
        // Process next item in queue
        if (ttsQueue.length > 0) {
            setTimeout(processNextTTS, 100); // Small delay between segments
        }
    }
}

function queueTTS(text) {
    if (voiceEnabled && text.trim()) {
        ttsQueue.push(text);
        processNextTTS();
    }
}

async function sendMessageStreaming(message) {
    try {
        console.log('🌊 Starting SSE streaming message');
        
        const messages = document.getElementById('messages');
        let currentTextDiv = null;
        let currentTextContent = null;
        
        return new Promise((resolve, reject) => {
            // Make POST request to start streaming
            fetch('/api/chat-stream', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ message })
            }).then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                console.log('✅ SSE connection established');
                
                // Read the stream
                const reader = response.body.getReader();
                const decoder = new TextDecoder();
                
                function readStream() {
                    reader.read().then(({ done, value }) => {
                        if (done) {
                            console.log('🏁 SSE stream completed');
                            resolve();
                            return;
                        }
                        
                        const chunk = decoder.decode(value, { stream: true });
                        const lines = chunk.split('\n');
                        
                        for (const line of lines) {
                            if (line.startsWith('data: ')) {
                                try {
                                    const data = JSON.parse(line.slice(6));
                                    handleSSEMessage(data);
                                } catch (e) {
                                    console.warn('⚠️ Failed to parse SSE data:', line);
                                }
                            }
                        }
                        
                        readStream(); // Continue reading
                    }).catch(error => {
                        console.error('❌ SSE stream error:', error);
                        reject(error);
                    });
                }
                
                readStream();
                
            }).catch(error => {
                console.error('❌ SSE connection error:', error);
                addMessage('Error', error.message, 'error');
                reject(error);
            });
            
            function handleSSEMessage(data) {
                console.log('📦 SSE message received:', data.type);
                
                switch (data.type) {
                    case 'text_segment':
                        // Create new text div for each segment (inline display)
                        console.log(`📝 Text segment ${data.segmentNumber}: ${data.content.length} chars`);
                        
                        currentTextDiv = document.createElement('div');
                        currentTextDiv.className = 'chat chat-start chat-message';
                        currentTextDiv.innerHTML = `
                            <div class="chat-image avatar">
                                <div class="w-10 rounded-full bg-primary flex items-center justify-center">
                                    <i data-lucide="bot" class="w-5 h-5 ml-4 text-primary-content"></i>
                                </div>
                            </div>
                            <div class="chat-header">Claude</div>
                            <div class="chat-bubble chat-bubble-primary">
                                <span class="text-segment"></span>
                            </div>
                        `;
                        messages.appendChild(currentTextDiv);
                        
                        currentTextContent = currentTextDiv.querySelector('.text-segment');
                        // Render markdown for text segments
                        currentTextContent.innerHTML = marked.parse(data.content);
                        
                        messages.scrollTop = messages.scrollHeight;
                        
                        // Initialize icons for new content
                        lucide.createIcons();
                        
                        // Queue text segment for TTS
                        queueTTS(data.content);
                        break;
                        
                    case 'tool_use':
                        // Real-time tool usage display (inline)
                        console.log(`🔧 Tool use: ${data.toolUse.name}`);
                        const toolDiv = document.createElement('div');
                        toolDiv.className = 'tool-card';
                        
                        const inputDisplay = JSON.stringify(data.toolUse.input, null, 2);
                        const inputLines = inputDisplay.split('\n');
                        const previewLines = inputLines.slice(0, 3).join('\n');
                        const hasMore = inputLines.length > 3;
                        
                        const toolId = 'tool_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
                        
                        toolDiv.innerHTML = `
                            <div class="card bg-info bg-opacity-10 border-l-4 border-info">
                                <div class="card-body p-4">
                                    <div class="card-title text-sm text-info">
                                        <i data-lucide="wrench" class="w-4 h-4 mr-1"></i>
                                        Used ${data.toolUse.name} tool
                                    </div>
                                    <div class="mockup-code text-xs">
                                        <div class="tool-preview code-content">${previewLines}</div>
                                        ${hasMore ? `
                                            <div class="tool-full code-content" id="${toolId}_full" style="display: none;">${inputDisplay}</div>
                                            <button class="btn btn-xs btn-ghost mt-2" onclick="toggleToolDetails('${toolId}')" id="${toolId}_btn">
                                                <i data-lucide="chevron-down" class="w-3 h-3 mr-1"></i>
                                                Show more...
                                            </button>
                                        ` : ''}
                                    </div>
                                </div>
                            </div>
                        `;
                        messages.appendChild(toolDiv);
                        messages.scrollTop = messages.scrollHeight;
                        
                        // Initialize icons for new content
                        lucide.createIcons();
                        break;
                        
                    case 'tool_result':
                        // Real-time tool result display (inline)
                        console.log(`📋 Tool result: ${data.toolResult.isError ? 'ERROR' : 'SUCCESS'}`);
                        const resultDiv = document.createElement('div');
                        resultDiv.className = 'tool-card';
                        
                        const alertType = data.toolResult.isError ? 'alert-error' : 'alert-success';
                        const borderColor = data.toolResult.isError ? 'border-error' : 'border-success';
                        const bgColor = data.toolResult.isError ? 'bg-error' : 'bg-success';
                        const statusText = data.toolResult.isError ? 'Error' : 'Result';
                        const statusIcon = data.toolResult.isError ? 'x-circle' : 'check-circle';
                        
                        resultDiv.innerHTML = `
                            <div class="card ${bgColor} bg-opacity-10 border-l-4 ${borderColor}">
                                <div class="card-body p-4">
                                    <div class="card-title text-sm ${data.toolResult.isError ? 'text-error' : 'text-success'}">
                                        <i data-lucide="${statusIcon}" class="w-4 h-4 mr-1"></i>
                                        Tool ${statusText}
                                    </div>
                                    <div class="mockup-code text-xs">
                                        <pre class="code-content">${data.toolResult.content}</pre>
                                    </div>
                                </div>
                            </div>
                        `;
                        messages.appendChild(resultDiv);
                        messages.scrollTop = messages.scrollHeight;
                        
                        // Initialize icons for new content
                        lucide.createIcons();
                        break;
                        
                    case 'complete':
                        // Stream completion
                        console.log('✅ SSE streaming completed');
                        console.log('📊 Final stats:', {
                            totalSegments: data.totalSegments
                        });
                        
                        // Update session display
                        if (data.sessionId) {
                            updateSessionDisplay(data.sessionId);
                        }
                        
                        resolve();
                        break;
                        
                    case 'error':
                        console.error('❌ SSE error:', data.error);
                        addMessage('Error', data.error, 'error');
                        reject(new Error(data.error));
                        break;
                        
                    default:
                        console.warn('⚠️ Unknown SSE message type:', data.type);
                }
            }
        });
        
    } catch (error) {
        console.error('❌ SSE streaming error:', error);
        addMessage('Error', error.message, 'error');
    }
}

function addMessage(sender, text, className) {
    const messages = document.getElementById('messages');
    const div = document.createElement('div');
    
    if (className === 'user') {
        div.className = 'chat chat-end chat-message';
        div.innerHTML = `
            <div class="chat-image avatar">
                <div class="w-10 rounded-full bg-secondary flex items-center justify-center">
                    <i data-lucide="user" class="w-5 h-5 text-secondary-content"></i>
                </div>
            </div>
            <div class="chat-header">You</div>
            <div class="chat-bubble chat-bubble-secondary">${text}</div>
        `;
    } else if (className === 'error') {
        div.className = 'tool-card';
        div.innerHTML = `
            <div class="alert alert-error">
                <i data-lucide="alert-circle" class="w-4 h-4"></i>
                <div>
                    <strong>Error:</strong> ${text}
                </div>
            </div>
        `;
    } else {
        // Claude message (fallback)
        div.className = 'chat chat-start chat-message';
        div.innerHTML = `
            <div class="chat-image avatar">
                <div class="w-10 rounded-full bg-primary flex items-center justify-center">
                    <i data-lucide="bot" class="w-5 h-5 text-primary-content"></i>
                </div>
            </div>
            <div class="chat-header">Claude</div>
            <div class="chat-bubble chat-bubble-primary">${text}</div>
        `;
    }
    
    messages.appendChild(div);
    messages.scrollTop = messages.scrollHeight;
    
    // Initialize icons for new content
    lucide.createIcons();
}

async function speakText(text) {
    return new Promise(async (resolve, reject) => {
        try {
            console.log('🔊 Requesting TTS for:', text);
            
            // Use same host as chat app but port 8001 for TTS service
            const ttsUrl = `http://${window.location.hostname}:8001/synthesize`;
            console.log('🌐 TTS URL:', ttsUrl);
            const audioResponse = await fetch(ttsUrl, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ text })
            });

            if (!audioResponse.ok) {
                throw new Error(`TTS service returned ${audioResponse.status}`);
            }

            // Create blob from response
            const audioBlob = await audioResponse.blob();
            const audioUrl = URL.createObjectURL(audioBlob);
            
            console.log('🎵 Audio blob created, size:', audioBlob.size);
            
            const audio = new Audio(audioUrl);
            
            // Add event listeners for debugging
            audio.addEventListener('loadstart', () => console.log('🔄 Audio loading started'));
            audio.addEventListener('canplay', () => console.log('✅ Audio can play'));
            audio.addEventListener('error', (e) => {
                console.error('❌ Audio error:', e);
                URL.revokeObjectURL(audioUrl);
                reject(e);
            });
            audio.addEventListener('ended', () => {
                console.log('🏁 Audio playback ended');
                URL.revokeObjectURL(audioUrl); // Clean up
                resolve(); // Resolve promise when audio finishes
            });
            
            await audio.play();
            console.log('🎉 Audio playback started');
        } catch (error) {
            console.error('❌ Speech synthesis failed:', error);
            reject(error);
        }
    });
}

function toggleVoice() {
    voiceEnabled = !voiceEnabled;
    const btn = document.getElementById('voiceBtn');
    const iconName = voiceEnabled ? 'mic' : 'mic-off';
    const status = voiceEnabled ? 'ON' : 'OFF';
    
    btn.innerHTML = `
        <i data-lucide="${iconName}" class="w-4 h-4 mr-2"></i>
        Voice: ${status}
    `;
    
    // Re-initialize icons for the updated button
    lucide.createIcons();
}


async function loadSession() {
    const projectInput = document.getElementById('projectInput');
    const sessionInput = document.getElementById('sessionInput');
    
    const project = projectInput.value.trim();
    const sessionId = sessionInput.value.trim();
    
    if (!project || !sessionId) {
        alert('Please enter both project and session ID');
        return;
    }
    
    console.log('🔄 Loading session:', project, sessionId);
    
    try {
        const response = await fetch('/api/load-session', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ project, sessionId })
        });
        
        const data = await response.json();
        
        if (!response.ok) {
            throw new Error(data.error || 'Failed to load session');
        }
        
        console.log('✅ Session loaded:', data);
        
        // Clear current messages
        const messagesContainer = document.getElementById('messages');
        messagesContainer.innerHTML = '';
        
        // Add loaded messages to chat UI
        for (const message of data.messages) {
            const sender = message.role === 'user' ? 'You' : 'Claude';
            const className = message.role === 'user' ? 'user' : 'claude';
            addMessage(sender, message.content, className);
        }
        
        // Clear the input fields
        projectInput.value = '';
        sessionInput.value = '';
        
        alert(`✅ Loaded ${data.messages.length} messages from session. You can now continue the conversation!`);
        
    } catch (error) {
        console.error('❌ Failed to load session:', error);
        alert(`❌ Failed to load session: ${error.message}`);
    }
}

async function clearSession() {
    console.log('🧹 Clearing session');
    
    try {
        const response = await fetch('/api/clear-session', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' }
        });
        
        const data = await response.json();
        
        if (!response.ok) {
            throw new Error(data.error || 'Failed to clear session');
        }
        
        console.log('✅ Session cleared:', data);
        
        // Clear the chat UI
        const messagesContainer = document.getElementById('messages');
        messagesContainer.innerHTML = '';
        
        // Clear session display
        updateSessionDisplay(null);
        
        alert('✅ Session cleared! Next message will start a fresh conversation.');
        
    } catch (error) {
        console.error('❌ Failed to clear session:', error);
        alert(`❌ Failed to clear session: ${error.message}`);
    }
}

// Toggle tool details visibility
function toggleToolDetails(toolId) {
    const preview = document.querySelector(`#${toolId}_full`).previousElementSibling;
    const full = document.getElementById(`${toolId}_full`);
    const btn = document.getElementById(`${toolId}_btn`);
    
    if (full.style.display === 'none') {
        // Show full
        preview.style.display = 'none';
        full.style.display = 'block';
        btn.innerHTML = '<i data-lucide="chevron-up" class="w-3 h-3 mr-1"></i>Show less...';
    } else {
        // Show preview
        preview.style.display = 'block';
        full.style.display = 'none';
        btn.innerHTML = '<i data-lucide="chevron-down" class="w-3 h-3 mr-1"></i>Show more...';
    }
    
    // Re-initialize icons for the new content
    lucide.createIcons();
}

// Enter key support
document.getElementById('messageInput').addEventListener('keypress', function(e) {
    if (e.key === 'Enter') {
        sendMessage();
    }
});

// Initialize Lucide icons when the page loads
document.addEventListener('DOMContentLoaded', function() {
    console.log('🎨 Initializing Lucide icons...');
    lucide.createIcons();
    console.log('✅ Lucide icons initialized');
});