// Global state management
let voiceEnabled = false;
let currentProject = null;
let currentSession = null;
let activeSessionId = null; // Track which session is currently active in UI

// TTS Queue management (keeping existing functionality)
let ttsQueue = [];
let isTTSPlaying = false;

async function processNextTTS() {
    if (isTTSPlaying || ttsQueue.length === 0) return;
    
    isTTSPlaying = true;
    const text = ttsQueue.shift();
    
    try {
        console.log('🔊 Playing TTS for segment:', text.substring(0, 50) + '...');
        await speakText(text);
    } catch (error) {
        console.error('❌ TTS error:', error);
    } finally {
        isTTSPlaying = false;
        if (ttsQueue.length > 0) {
            setTimeout(processNextTTS, 100);
        }
    }
}

function queueTTS(text) {
    if (voiceEnabled && text.trim()) {
        ttsQueue.push(text);
        processNextTTS();
    }
}

// Initialize application
document.addEventListener('DOMContentLoaded', function() {
    console.log('🎨 Initializing Claude Chat Application...');
    lucide.createIcons();
    loadProjects();
    setupEventListeners();
    setupMobileInteractions();
    console.log('✅ Application initialized');
});

function setupEventListeners() {
    // Enter key support for message input
    document.getElementById('messageInput').addEventListener('keypress', function(e) {
        if (e.key === 'Enter' && !this.disabled) {
            sendMessage();
        }
    });
    
    // Auto-fill project name from path
    document.getElementById('projectPathInput').addEventListener('input', function(e) {
        const path = e.target.value.trim();
        if (path) {
            const pathParts = path.split('/');
            const folderName = pathParts[pathParts.length - 1];
            if (folderName) {
                document.getElementById('projectNameInput').value = folderName;
            }
        }
    });
}

// Project Management Functions

async function loadProjects() {
    try {
        console.log('📋 Loading projects...');
        const response = await fetch('/api/projects');
        const data = await response.json();
        
        if (!response.ok) {
            throw new Error(data.error || 'Failed to load projects');
        }
        
        console.log('✅ Loaded projects:', data);
        renderProjectsList(data.projects);
        
    } catch (error) {
        console.error('❌ Error loading projects:', error);
        showAlert('Failed to load projects: ' + error.message, 'error');
    }
}

function renderProjectsList(projects) {
    const projectsList = document.getElementById('projectsList');
    
    if (projects.length === 0) {
        projectsList.innerHTML = `
            <div class="text-center text-base-content/50 py-8">
                <i data-lucide="folder-plus" class="w-12 h-12 mx-auto mb-2 opacity-50"></i>
                <p>No projects yet</p>
                <p class="text-sm">Click + to create one</p>
            </div>
        `;
        lucide.createIcons();
        return;
    }
    
    projectsList.innerHTML = projects.map(project => `
        <div class="project-accordion collapse collapse-arrow bg-base-100 border border-base-300">
            <input type="checkbox" class="min-h-0">
            <div class="collapse-title pr-12">
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <i data-lucide="folder" class="w-4 h-4 mr-2 text-primary"></i>
                        <span class="font-medium">${project.name}</span>
                    </div>
                    <div class="flex items-center gap-1">
                        <button onclick="openNewSessionModal(${project.id})" 
                                class="btn btn-ghost btn-xs z-50 relative" 
                                title="New Session"
                                onclick="event.stopPropagation()">
                            <i data-lucide="plus" class="w-3 h-3"></i>
                        </button>
                        <button onclick="deleteProject(${project.id})" 
                                class="btn btn-ghost btn-xs text-error z-50 relative" 
                                title="Delete Project"
                                onclick="event.stopPropagation()">
                            <i data-lucide="trash-2" class="w-3 h-3"></i>
                        </button>
                    </div>
                </div>
                <div class="text-xs text-base-content/50 mt-1">${project.path}</div>
            </div>
            <div class="collapse-content">
                <div class="pt-2">
                    ${renderSessionsList(project.sessions)}
                </div>
            </div>
        </div>
    `).join('');
    
    lucide.createIcons();
}

function renderSessionsList(sessions) {
    if (sessions.length === 0) {
        return `
            <div class="text-center text-base-content/30 py-4">
                <i data-lucide="plus-circle" class="w-8 h-8 mx-auto mb-2 opacity-50"></i>
                <p class="text-sm">No sessions yet</p>
            </div>
        `;
    }
    
    return sessions.map(session => `
        <div class="session-item p-3 rounded cursor-pointer ${session.id === activeSessionId ? 'active' : ''}" 
             onclick="selectSession(${session.project_id}, ${session.id})">
            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    <i data-lucide="message-square" class="w-4 h-4 mr-2 text-secondary"></i>
                    <span class="font-medium">${session.name}</span>
                </div>
                <button onclick="deleteSession(${session.id}); event.stopPropagation();" 
                        class="btn btn-ghost btn-xs text-error opacity-50 hover:opacity-100">
                    <i data-lucide="x" class="w-3 h-3"></i>
                </button>
            </div>
            <div class="text-xs text-base-content/50 mt-1">
                ${session.session_id ? 'Session ID: ' + session.session_id.substring(0, 8) + '...' : 'New session'}
            </div>
        </div>
    `).join('');
}

// Modal Functions

function openNewProjectModal() {
    document.getElementById('newProjectModal').showModal();
    document.getElementById('projectPathInput').focus();
}

function closeNewProjectModal() {
    document.getElementById('newProjectModal').close();
    document.getElementById('projectPathInput').value = '';
    document.getElementById('projectNameInput').value = '';
}

function openNewSessionModal(projectId) {
    currentProject = { id: projectId };
    document.getElementById('newSessionModal').showModal();
    document.getElementById('sessionNameInput').focus();
}

function closeNewSessionModal() {
    document.getElementById('newSessionModal').close();
    document.getElementById('sessionNameInput').value = '';
    currentProject = null;
}

// Project CRUD Operations

async function createProject() {
    const pathInput = document.getElementById('projectPathInput');
    const nameInput = document.getElementById('projectNameInput');
    
    const projectPath = pathInput.value.trim();
    const projectName = nameInput.value.trim();
    
    if (!projectPath) {
        showAlert('Project path is required', 'error');
        pathInput.focus();
        return;
    }
    
    try {
        console.log('🆕 Creating project:', projectName || 'auto-named', 'at', projectPath);
        
        // Validate path first
        const validationResponse = await fetch(`/api/validate-path?path=${encodeURIComponent(projectPath)}`);
        const validation = await validationResponse.json();
        
        if (!validation.valid) {
            throw new Error('Invalid path: Directory does not exist or is not accessible');
        }
        
        // Create project
        const response = await fetch('/api/projects', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                name: projectName || projectPath.split('/').pop(),
                path: projectPath
            })
        });
        
        const project = await response.json();
        
        if (!response.ok) {
            throw new Error(project.error || 'Failed to create project');
        }
        
        console.log('✅ Project created:', project);
        showAlert('Project created successfully!', 'success');
        closeNewProjectModal();
        loadProjects(); // Refresh projects list
        
    } catch (error) {
        console.error('❌ Error creating project:', error);
        showAlert('Failed to create project: ' + error.message, 'error');
    }
}

async function deleteProject(projectId) {
    if (!confirm('Are you sure you want to delete this project and all its sessions?')) {
        return;
    }
    
    try {
        console.log('🗑️ Deleting project:', projectId);
        
        const response = await fetch(`/api/projects/${projectId}`, {
            method: 'DELETE'
        });
        
        const result = await response.json();
        
        if (!response.ok) {
            throw new Error(result.error || 'Failed to delete project');
        }
        
        console.log('✅ Project deleted');
        showAlert('Project deleted successfully', 'success');
        
        // If the deleted project was active, clear the session
        if (currentSession && currentSession.project_id === projectId) {
            clearActiveSession();
        }
        
        loadProjects(); // Refresh projects list
        
    } catch (error) {
        console.error('❌ Error deleting project:', error);
        showAlert('Failed to delete project: ' + error.message, 'error');
    }
}

// Session CRUD Operations

async function createSession() {
    const nameInput = document.getElementById('sessionNameInput');
    const sessionName = nameInput.value.trim();
    
    if (!sessionName) {
        showAlert('Session name is required', 'error');
        nameInput.focus();
        return;
    }
    
    if (!currentProject) {
        showAlert('No project selected', 'error');
        return;
    }
    
    try {
        console.log('🆕 Creating session:', sessionName, 'for project', currentProject.id);
        
        const response = await fetch(`/api/projects/${currentProject.id}/sessions`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ name: sessionName })
        });
        
        const session = await response.json();
        
        if (!response.ok) {
            throw new Error(session.error || 'Failed to create session');
        }
        
        console.log('✅ Session created:', session);
        showAlert('Session created successfully!', 'success');
        closeNewSessionModal();
        loadProjects(); // Refresh projects list
        
        // Auto-select the new session
        selectSession(session.project_id, session.id);
        
    } catch (error) {
        console.error('❌ Error creating session:', error);
        showAlert('Failed to create session: ' + error.message, 'error');
    }
}

async function deleteSession(sessionId) {
    if (!confirm('Are you sure you want to delete this session?')) {
        return;
    }
    
    try {
        console.log('🗑️ Deleting session:', sessionId);
        
        const response = await fetch(`/api/sessions/${sessionId}`, {
            method: 'DELETE'
        });
        
        const result = await response.json();
        
        if (!response.ok) {
            throw new Error(result.error || 'Failed to delete session');
        }
        
        console.log('✅ Session deleted');
        showAlert('Session deleted successfully', 'success');
        
        // If the deleted session was active, clear it
        if (currentSession && currentSession.id === sessionId) {
            clearActiveSession();
        }
        
        loadProjects(); // Refresh projects list
        
    } catch (error) {
        console.error('❌ Error deleting session:', error);
        showAlert('Failed to delete session: ' + error.message, 'error');
    }
}

// Session Selection and Management

async function selectSession(projectId, sessionId) {
    try {
        console.log('🎯 Selecting session:', sessionId, 'from project:', projectId);
        
        // Get fresh project data to find the session
        const response = await fetch('/api/projects');
        const data = await response.json();
        
        if (!response.ok) {
            throw new Error(data.error || 'Failed to load projects');
        }
        
        // Find the project and session
        const project = data.projects.find(p => p.id === projectId);
        if (!project) {
            throw new Error('Project not found');
        }
        
        const session = project.sessions.find(s => s.id === sessionId);
        if (!session) {
            throw new Error('Session not found');
        }
        
        // Set current session
        currentSession = session;
        currentProject = project;
        activeSessionId = sessionId;
        
        // Tell the backend about the active session switch
        try {
            await fetch('/api/set-active-session', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    sessionId: session.id,
                    claudeSessionId: session.session_id
                })
            });
        } catch (error) {
            console.warn('⚠️ Failed to set active session:', error);
        }
        
        // Update UI
        updateSessionHeader(session);
        showChatInterface();
        loadSessionHistory(session);
        
        // Update active session highlighting
        loadProjects(); // This will re-render with current activeSessionId
        
        console.log('✅ Session selected:', session.name);
        
    } catch (error) {
        console.error('❌ Error selecting session:', error);
        showAlert('Failed to select session: ' + error.message, 'error');
    }
}

function clearActiveSession() {
    currentSession = null;
    currentProject = null;
    activeSessionId = null;
    
    // Show welcome screen
    document.getElementById('welcomeScreen').style.display = 'flex';
    document.getElementById('sessionHeader').style.display = 'none';
    document.getElementById('chatInterface').style.display = 'none';
    
    // Clear messages
    document.getElementById('messages').innerHTML = '';
    
    // Disable input
    document.getElementById('messageInput').disabled = true;
    document.getElementById('sendBtn').disabled = true;
}

function updateSessionHeader(session) {
    document.getElementById('sessionTitle').textContent = session.name;
    document.getElementById('sessionInfo').textContent = 
        session.session_id 
            ? `Session ID: ${session.session_id}` 
            : 'New session - send a message to start';
}

function showChatInterface() {
    document.getElementById('welcomeScreen').style.display = 'none';
    document.getElementById('sessionHeader').style.display = 'block';
    document.getElementById('chatInterface').style.display = 'flex';
    
    // Enable input
    document.getElementById('messageInput').disabled = false;
    document.getElementById('sendBtn').disabled = false;
    document.getElementById('messageInput').focus();
}

async function loadSessionHistory(session) {
    if (!session.session_id) {
        // New session - no history to load
        document.getElementById('messages').innerHTML = '';
        return;
    }
    
    try {
        console.log('📜 Loading session history for:', session.session_id);
        
        // Use the existing load-session endpoint but adapt it
        // Convert path to Claude's project directory format (e.g., /Users/<USER>/Documents/gennext -> -Users-jaypears-Documents-gennext)
        const claudeProjectName = currentProject.path.replace(/\//g, '-');
        
        const response = await fetch('/api/load-session', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                project: claudeProjectName, // Use Claude's project directory naming convention
                sessionId: session.session_id
            })
        });
        
        const data = await response.json();
        
        if (!response.ok) {
            console.warn('⚠️ Could not load session history:', data.error);
            // Continue anyway - might be a new session
            return;
        }
        
        // Clear current messages
        const messagesContainer = document.getElementById('messages');
        messagesContainer.innerHTML = '';
        
        // Add loaded messages to chat UI
        for (const message of data.messages) {
            const sender = message.role === 'user' ? 'You' : 'Claude';
            const className = message.role === 'user' ? 'user' : 'claude';
            addMessage(sender, message.content, className);
        }
        
        console.log('✅ Loaded session history:', data.messages.length, 'messages');
        
    } catch (error) {
        console.error('❌ Error loading session history:', error);
        // Continue anyway - session might be new
    }
}

// Message Sending (Enhanced from original)

async function sendMessage() {
    const input = document.getElementById('messageInput');
    const message = input.value.trim();
    if (!message) return;
    
    if (!currentSession) {
        showAlert('Please select a session first', 'error');
        return;
    }
    
    addMessage('You', message, 'user');
    input.value = '';
    
    // Set project path for the chat service
    try {
        await fetch('/api/set-project-path', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ path: currentProject.path })
        });
    } catch (error) {
        console.warn('⚠️ Failed to set project path:', error);
    }
    
    // Send message using existing streaming functionality
    await sendMessageStreaming(message);
}

// Keep all existing streaming, TTS, and message handling functions from original app.js
// [Previous sendMessageStreaming, addMessage, speakText, toggleVoice functions remain the same]

async function sendMessageStreaming(message) {
    try {
        console.log('🌊 Starting SSE streaming message');
        
        const messages = document.getElementById('messages');
        let currentTextDiv = null;
        let currentTextContent = null;
        
        return new Promise((resolve, reject) => {
            fetch('/api/chat-stream', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ message })
            }).then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                console.log('✅ SSE connection established');
                
                const reader = response.body.getReader();
                const decoder = new TextDecoder();
                
                function readStream() {
                    reader.read().then(({ done, value }) => {
                        if (done) {
                            console.log('🏁 SSE stream completed');
                            resolve();
                            return;
                        }
                        
                        const chunk = decoder.decode(value, { stream: true });
                        const lines = chunk.split('\\n');
                        
                        for (const line of lines) {
                            if (line.startsWith('data: ')) {
                                try {
                                    const data = JSON.parse(line.slice(6));
                                    handleSSEMessage(data);
                                } catch (e) {
                                    console.warn('⚠️ Failed to parse SSE data:', line);
                                }
                            }
                        }
                        
                        readStream();
                    }).catch(error => {
                        console.error('❌ SSE stream error:', error);
                        reject(error);
                    });
                }
                
                readStream();
                
            }).catch(error => {
                console.error('❌ SSE connection error:', error);
                addMessage('Error', error.message, 'error');
                reject(error);
            });
            
            function handleSSEMessage(data) {
                console.log('📦 SSE message received:', data.type);
                
                switch (data.type) {
                    case 'text_segment':
                        currentTextDiv = document.createElement('div');
                        currentTextDiv.className = 'chat chat-start chat-message';
                        currentTextDiv.innerHTML = `
                            <div class="chat-image avatar">
                                <div class="w-10 rounded-full bg-primary flex items-center justify-center">
                                    <i data-lucide="bot" class="w-5 h-5 ml-4 text-primary-content"></i>
                                </div>
                            </div>
                            <div class="chat-header">Claude</div>
                            <div class="chat-bubble chat-bubble-primary">
                                <span class="text-segment"></span>
                            </div>
                        `;
                        messages.appendChild(currentTextDiv);
                        
                        currentTextContent = currentTextDiv.querySelector('.text-segment');
                        currentTextContent.innerHTML = marked.parse(data.content);
                        
                        messages.scrollTop = messages.scrollHeight;
                        lucide.createIcons();
                        queueTTS(data.content);
                        break;
                        
                    case 'tool_use':
                        const toolDiv = document.createElement('div');
                        toolDiv.className = 'tool-card';
                        
                        const inputDisplay = JSON.stringify(data.toolUse.input, null, 2);
                        const inputLines = inputDisplay.split('\\n');
                        const previewLines = inputLines.slice(0, 3).join('\\n');
                        const hasMore = inputLines.length > 3;
                        
                        const toolId = 'tool_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
                        
                        toolDiv.innerHTML = `
                            <div class="card bg-info bg-opacity-10 border-l-4 border-info">
                                <div class="card-body p-4">
                                    <div class="card-title text-sm text-info">
                                        <i data-lucide="wrench" class="w-4 h-4 mr-1"></i>
                                        Used ${data.toolUse.name} tool
                                    </div>
                                    <div class="mockup-code text-xs">
                                        <div class="tool-preview code-content">${previewLines}</div>
                                        ${hasMore ? `
                                            <div class="tool-full code-content" id="${toolId}_full" style="display: none;">${inputDisplay}</div>
                                            <button class="btn btn-xs btn-ghost mt-2" onclick="toggleToolDetails('${toolId}')" id="${toolId}_btn">
                                                <i data-lucide="chevron-down" class="w-3 h-3 mr-1"></i>
                                                Show more...
                                            </button>
                                        ` : ''}
                                    </div>
                                </div>
                            </div>
                        `;
                        messages.appendChild(toolDiv);
                        messages.scrollTop = messages.scrollHeight;
                        lucide.createIcons();
                        break;
                        
                    case 'tool_result':
                        const resultDiv = document.createElement('div');
                        resultDiv.className = 'tool-card';
                        
                        const alertType = data.toolResult.isError ? 'alert-error' : 'alert-success';
                        const borderColor = data.toolResult.isError ? 'border-error' : 'border-success';
                        const bgColor = data.toolResult.isError ? 'bg-error' : 'bg-success';
                        const statusText = data.toolResult.isError ? 'Error' : 'Result';
                        const statusIcon = data.toolResult.isError ? 'x-circle' : 'check-circle';
                        
                        resultDiv.innerHTML = `
                            <div class="card ${bgColor} bg-opacity-10 border-l-4 ${borderColor}">
                                <div class="card-body p-4">
                                    <div class="card-title text-sm ${data.toolResult.isError ? 'text-error' : 'text-success'}">
                                        <i data-lucide="${statusIcon}" class="w-4 h-4 mr-1"></i>
                                        Tool ${statusText}
                                    </div>
                                    <div class="mockup-code text-xs">
                                        <pre class="code-content">${data.toolResult.content}</pre>
                                    </div>
                                </div>
                            </div>
                        `;
                        messages.appendChild(resultDiv);
                        messages.scrollTop = messages.scrollHeight;
                        lucide.createIcons();
                        break;
                        
                    case 'complete':
                        console.log('✅ SSE streaming completed');
                        
                        // Handle session ID updates
                        if (currentSession) {
                            if (data.forkedSessionId) {
                                // Forked session ID - store for future resume
                                console.log('🔀 Fork detected, storing new session ID:', data.forkedSessionId);
                                updateSessionId(currentSession.id, data.forkedSessionId);
                            } else if (data.sessionId && !currentSession.session_id) {
                                // Fresh session ID - store initial session ID
                                console.log('🆕 Fresh session, storing initial session ID:', data.sessionId);
                                updateSessionId(currentSession.id, data.sessionId);
                            }
                        }
                        
                        resolve();
                        break;
                        
                    case 'error':
                        console.error('❌ SSE error:', data.error);
                        addMessage('Error', data.error, 'error');
                        reject(new Error(data.error));
                        break;
                        
                    default:
                        console.warn('⚠️ Unknown SSE message type:', data.type);
                }
            }
        });
        
    } catch (error) {
        console.error('❌ SSE streaming error:', error);
        addMessage('Error', error.message, 'error');
    }
}

function addMessage(sender, text, className) {
    const messages = document.getElementById('messages');
    const div = document.createElement('div');
    
    if (className === 'user') {
        div.className = 'chat chat-end chat-message';
        div.innerHTML = `
            <div class="chat-image avatar">
                <div class="w-10 rounded-full bg-secondary flex items-center justify-center">
                    <i data-lucide="user" class="w-5 h-5 text-secondary-content"></i>
                </div>
            </div>
            <div class="chat-header">You</div>
            <div class="chat-bubble chat-bubble-secondary">${text}</div>
        `;
    } else if (className === 'error') {
        div.className = 'tool-card';
        div.innerHTML = `
            <div class="alert alert-error">
                <i data-lucide="alert-circle" class="w-4 h-4"></i>
                <div>
                    <strong>Error:</strong> ${text}
                </div>
            </div>
        `;
    } else {
        div.className = 'chat chat-start chat-message';
        div.innerHTML = `
            <div class="chat-image avatar">
                <div class="w-10 rounded-full bg-primary flex items-center justify-center">
                    <i data-lucide="bot" class="w-5 h-5 text-primary-content"></i>
                </div>
            </div>
            <div class="chat-header">Claude</div>
            <div class="chat-bubble chat-bubble-primary">${text}</div>
        `;
    }
    
    messages.appendChild(div);
    messages.scrollTop = messages.scrollHeight;
    lucide.createIcons();
}

async function speakText(text) {
    return new Promise(async (resolve, reject) => {
        try {
            console.log('🔊 Requesting TTS for:', text);
            
            const ttsUrl = `http://${window.location.hostname}:8001/synthesize`;
            console.log('🌐 TTS URL:', ttsUrl);
            const audioResponse = await fetch(ttsUrl, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ text })
            });

            if (!audioResponse.ok) {
                throw new Error(`TTS service returned ${audioResponse.status}`);
            }

            const audioBlob = await audioResponse.blob();
            const audioUrl = URL.createObjectURL(audioBlob);
            
            console.log('🎵 Audio blob created, size:', audioBlob.size);
            
            const audio = new Audio(audioUrl);
            
            audio.addEventListener('loadstart', () => console.log('🔄 Audio loading started'));
            audio.addEventListener('canplay', () => console.log('✅ Audio can play'));
            audio.addEventListener('error', (e) => {
                console.error('❌ Audio error:', e);
                URL.revokeObjectURL(audioUrl);
                reject(e);
            });
            audio.addEventListener('ended', () => {
                console.log('🏁 Audio playback ended');
                URL.revokeObjectURL(audioUrl);
                resolve();
            });
            
            await audio.play();
            console.log('🎉 Audio playback started');
        } catch (error) {
            console.error('❌ Speech synthesis failed:', error);
            reject(error);
        }
    });
}

function toggleVoice() {
    voiceEnabled = !voiceEnabled;
    const btn = document.getElementById('voiceBtn');
    const iconName = voiceEnabled ? 'mic' : 'mic-off';
    const status = voiceEnabled ? 'ON' : 'OFF';
    
    btn.innerHTML = `
        <i data-lucide="${iconName}" class="w-4 h-4 mr-2"></i>
        <span class="hidden sm:inline">Voice: ${status}</span>
        <span class="sm:hidden">${status}</span>
    `;
    
    lucide.createIcons();
}

// Utility Functions

async function updateSessionId(sessionDbId, claudeSessionId) {
    try {
        console.log('🔄 Updating session ID:', sessionDbId, '->', claudeSessionId);
        
        const response = await fetch(`/api/sessions/${sessionDbId}/session-id`, {
            method: 'PUT',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ sessionId: claudeSessionId })
        });
        
        const result = await response.json();
        
        if (!response.ok) {
            throw new Error(result.error || 'Failed to update session ID');
        }
        
        // Update current session object
        if (currentSession && currentSession.id === sessionDbId) {
            currentSession.session_id = claudeSessionId;
            updateSessionHeader(currentSession);
        }
        
        console.log('✅ Session ID updated successfully');
        
    } catch (error) {
        console.error('❌ Error updating session ID:', error);
    }
}

function toggleToolDetails(toolId) {
    const preview = document.querySelector(`#${toolId}_full`).previousElementSibling;
    const full = document.getElementById(`${toolId}_full`);
    const btn = document.getElementById(`${toolId}_btn`);
    
    if (full.style.display === 'none') {
        preview.style.display = 'none';
        full.style.display = 'block';
        btn.innerHTML = '<i data-lucide="chevron-up" class="w-3 h-3 mr-1"></i>Show less...';
    } else {
        preview.style.display = 'block';
        full.style.display = 'none';
        btn.innerHTML = '<i data-lucide="chevron-down" class="w-3 h-3 mr-1"></i>Show more...';
    }
    
    lucide.createIcons();
}

function showAlert(message, type = 'info') {
    // Simple alert for now - could be enhanced with toast notifications
    const alertClass = type === 'error' ? 'alert-error' : 
                       type === 'success' ? 'alert-success' : 'alert-info';
    
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert ${alertClass} fixed top-4 right-4 max-w-md z-50`;
    alertDiv.innerHTML = `
        <i data-lucide="${type === 'error' ? 'alert-circle' : type === 'success' ? 'check-circle' : 'info'}" class="w-4 h-4"></i>
        <span>${message}</span>
    `;
    
    document.body.appendChild(alertDiv);
    lucide.createIcons();
    
    setTimeout(() => {
        alertDiv.remove();
    }, 5000);
}

// Mobile Sidebar Functions
function toggleMobileSidebar() {
    const sidebar = document.getElementById('sidebar');
    const overlay = document.getElementById('sidebarOverlay');
    
    if (sidebar.classList.contains('open')) {
        closeMobileSidebar();
    } else {
        openMobileSidebar();
    }
}

function openMobileSidebar() {
    const sidebar = document.getElementById('sidebar');
    const overlay = document.getElementById('sidebarOverlay');
    
    sidebar.classList.add('open');
    overlay.classList.add('active');
    document.body.style.overflow = 'hidden';
}

function closeMobileSidebar() {
    const sidebar = document.getElementById('sidebar');
    const overlay = document.getElementById('sidebarOverlay');
    
    sidebar.classList.remove('open');
    overlay.classList.remove('active');
    document.body.style.overflow = '';
}

// Mobile Interaction Setup
function setupMobileInteractions() {
    // Handle orientation changes
    window.addEventListener('orientationchange', function() {
        setTimeout(() => {
            // Force a repaint after orientation change
            document.body.style.height = '100vh';
            setTimeout(() => {
                document.body.style.height = '';
            }, 100);
        }, 500);
    });
    
    // Handle window resize for responsive behavior
    window.addEventListener('resize', function() {
        const sidebar = document.getElementById('sidebar');
        const overlay = document.getElementById('sidebarOverlay');
        
        // Close mobile sidebar on desktop resize
        if (window.innerWidth > 768) {
            sidebar.classList.remove('open');
            overlay.classList.remove('active');
            document.body.style.overflow = '';
        }
    });
    
    // Touch event handling for better mobile interactions
    let touchStartX = 0;
    let touchStartY = 0;
    
    document.addEventListener('touchstart', function(e) {
        touchStartX = e.touches[0].clientX;
        touchStartY = e.touches[0].clientY;
    }, { passive: true });
    
    document.addEventListener('touchmove', function(e) {
        if (!touchStartX || !touchStartY) return;
        
        const touchEndX = e.touches[0].clientX;
        const touchEndY = e.touches[0].clientY;
        
        const diffX = touchStartX - touchEndX;
        const diffY = touchStartY - touchEndY;
        
        // Swipe gestures for sidebar (only on mobile)
        if (window.innerWidth <= 768) {
            // Swipe right to open sidebar
            if (diffX < -100 && Math.abs(diffY) < 50 && touchStartX < 50) {
                openMobileSidebar();
            }
            // Swipe left to close sidebar
            else if (diffX > 100 && Math.abs(diffY) < 50) {
                const sidebar = document.getElementById('sidebar');
                if (sidebar.classList.contains('open')) {
                    closeMobileSidebar();
                }
            }
        }
        
        touchStartX = 0;
        touchStartY = 0;
    }, { passive: true });
    
    // Prevent zoom on double tap for buttons
    let lastTouchEnd = 0;
    document.addEventListener('touchend', function(event) {
        const now = (new Date()).getTime();
        if (now - lastTouchEnd <= 300) {
            if (event.target.closest('button, .btn, input, textarea')) {
                event.preventDefault();
            }
        }
        lastTouchEnd = now;
    }, false);
    
    // Auto-close mobile sidebar when selecting a session
    const originalSelectSession = window.selectSession;
    window.selectSession = function(projectId, sessionId) {
        if (window.innerWidth <= 768) {
            closeMobileSidebar();
        }
        return originalSelectSession.call(this, projectId, sessionId);
    };
    
    // Enhance form submission on mobile
    const forms = document.querySelectorAll('input');
    forms.forEach(input => {
        input.addEventListener('focus', function() {
            // Prevent viewport zooming on input focus for mobile
            if (window.innerWidth <= 768) {
                this.style.fontSize = '16px';
            }
        });
    });
    
    // Add visual feedback for touch interactions
    document.addEventListener('touchstart', function(e) {
        const target = e.target.closest('button, .btn, .session-item, .collapse-title');
        if (target) {
            target.style.transform = 'scale(0.98)';
            target.style.transition = 'transform 0.1s ease';
        }
    }, { passive: true });
    
    document.addEventListener('touchend', function(e) {
        const target = e.target.closest('button, .btn, .session-item, .collapse-title');
        if (target) {
            setTimeout(() => {
                target.style.transform = '';
                target.style.transition = '';
            }, 100);
        }
    }, { passive: true });
}