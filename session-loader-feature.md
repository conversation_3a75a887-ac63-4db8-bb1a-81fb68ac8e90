# Session Loader Feature

## Overview
Adding a simple session loading capability to the Claude Voice Chat app that allows resuming existing Claude Code conversations by loading their JSONL history files.

## Feature Scope
This is a **proof-of-concept implementation** to test if Claude Code SDK sessions can be resumed by pre-loading conversation history from the projects folder.

## User Experience
1. User sees new UI elements above/below the chat area:
   - **Project input field**: Manual entry of encoded project path (e.g., `-Users-jaypears-Documents-claude-chat-chat-app`)
   - **Session ID input field**: Manual entry of session UUID (e.g., `c513468f-9afb-4f65-bffe-822b0661497e`)
   - **Load button**: Triggers the session loading process

2. When Load is clicked:
   - System validates project and session exist
   - Parses JSONL file to extract user/assistant messages
   - Populates chat UI with historical messages
   - Sets session ID in ChatService for continuation
   - User can immediately continue chatting as if they never left

## Technical Implementation

### Data Source
- **Location**: `~/.claude/projects/[encoded-project-path]/[session-id].jsonl`
- **Format**: Each line is a JSON object containing message data
- **Target data**: Lines with `type: "user"` and `type: "assistant"`

### Parsing Strategy
```javascript
// Parse each JSONL line
const sessionData = JSON.parse(line);

// Filter for conversation messages only
if (sessionData.type === 'user' || sessionData.type === 'assistant') {
    // Extract message content and role
    // Add to chat UI using existing addMessage() function
}
```

### Session Continuation
- Extract sessionId from JSONL metadata
- Set `ChatService.sessionId = loadedSessionId`
- Normal SDK conversation flow continues from that point

## What Won't Change
- **Voice capabilities**: TTS continues to work exactly as before
- **Current chat flow**: SDK integration remains untouched
- **UI appearance**: Messages display identically to live conversation
- **Audio behavior**: Claude responses still trigger voice synthesis when enabled

## Validation Requirements
1. **Project exists**: Check if `~/.claude/projects/[project-name]/` directory exists
2. **Session exists**: Verify `[session-id].jsonl` file is present
3. **Valid JSONL**: Basic parsing validation
4. **Has messages**: At least one user/assistant message pair

## Error Handling
- Invalid project name → Show error, don't load
- Invalid session ID → Show error, don't load  
- Corrupted JSONL → Show error, load what's parseable
- No messages found → Show warning, but set session ID for continuation

## Future Improvements (Not in This Implementation)
- Project dropdown/autocomplete
- Session browser with timestamps
- Search across conversations
- Better error messages
- UI polish

## Success Criteria
User can:
1. Enter a valid project and session ID
2. Click Load to populate chat history
3. Continue the conversation seamlessly
4. Hear voice responses for new messages (existing functionality preserved)

This feature essentially bootstraps the chat UI from Claude Code's session storage, then hands control back to the normal SDK flow.