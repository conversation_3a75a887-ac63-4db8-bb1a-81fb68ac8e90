# Claude Voice Chat

A simple chat application using Claude Code SDK (TypeScript) and Kokoro TTS (Python) with voice synthesis capabilities.

## Features

- 💬 **Text chat** with Claude Code SDK integration
- 🔊 **Voice synthesis** using Kokoro TTS (af_heart voice)
- 🔄 **Conversation history** maintained automatically
- 🌐 **Network access** - works across devices on local network
- 🎛️ **Voice toggle** - text-first design with optional audio

## Architecture

```
Browser ←→ Chat App (Node.js) ←→ Claude Code SDK
   ↕
TTS Service (Python/Kokoro)
```

## Quick Start

### Prerequisites

- Node.js 18+
- Python 3.10+
- conda (recommended for Python environment)

### 1. Setup TTS Service

```bash
# Create conda environment
conda create -n claude-chat python=3.10 -y
conda activate claude-chat

# Install dependencies
cd tts-service
pip install -r requirements.txt

# Start TTS service
python main.py
```

### 2. Setup Chat App

```bash
# Install dependencies
cd chat-app
npm install

# Start chat app
npm run dev
```

### 3. Access the App

- **Local**: http://localhost:3000
- **Network**: http://[your-ip]:3000

## Project Structure

```
claude-chat/
├── chat-app/           # TypeScript/Node.js app
│   ├── src/
│   │   ├── chat.ts     # Claude SDK integration with session management
│   │   ├── audio.ts    # TTS service client
│   │   └── server.ts   # Express API server
│   ├── public/
│   │   ├── index.html  # Chat interface
│   │   └── app.js      # Frontend logic with voice toggle
│   └── package.json
└── tts-service/        # Python TTS service
    ├── main.py         # FastAPI server with CORS
    ├── tts.py          # Kokoro TTS wrapper
    └── requirements.txt
```

## Key Implementation Details

### Session Management
- Uses Claude Code SDK's built-in session handling
- Conversation history maintained automatically
- Ready for multi-session expansion

### Audio Architecture
- Hardcoded `af_heart` voice for MVP
- WAV format for quality and compatibility
- Direct browser-to-TTS communication
- Proper CORS setup for network access

### Network Configuration
- TTS service: `0.0.0.0:8001` (accepts all connections)
- Chat app: `localhost:3000` with bridge network support
- Dynamic host detection for cross-device TTS access

## Development Notes

- **Text-first design**: App works even if TTS fails
- **Error handling**: Comprehensive logging and fallback behavior
- **Memory management**: Proper blob URL cleanup
- **CORS**: Configured for local network development

## Future Enhancements

- Multiple voice selection
- Voice input (speech-to-text)
- Multi-chat sessions
- Persistent chat history
- UI improvements
- Docker containerization

## Troubleshooting

- **No audio on remote devices**: Check TTS service CORS configuration
- **Session not persisting**: Verify Claude Code SDK authentication
- **TTS errors**: Check conda environment and Kokoro installation
- **Network access issues**: Verify bridge network and firewall settings