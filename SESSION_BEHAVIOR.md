# Claude Code SDK Session Behavior

## Session Creation and Management

### Fresh Sessions
- Created when no sessionId is provided to the conversation builder
- <PERSON> Code CLI generates a new UUID for the session
- Creates a new JSONL file named `{sessionId}.jsonl` in the project directory
- Returns the session ID in the first stream response

### Session Resumption with `.withSessionId()`
- When using `.withSessionId(existingSessionId)`, <PERSON> Code CLI:
  1. **Loads conversation history** from the existing JSONL file
  2. **Creates a NEW session ID** for the continuation (fork behavior)
  3. **Creates a NEW JSONL file** with the new session ID
  4. **Includes loaded history + new messages** in the new file
  5. **Returns the NEW session ID** in stream responses

### Key Insight: "Resume and Fork" Behavior
Claude Code CLI treats session resumption as creating a new branch rather than continuing in-place:
- ✅ **Context is preserved** - <PERSON> remembers previous conversation
- ✅ **History is maintained** - Old messages are included in new file
- 🆕 **New session created** - Fresh session ID and file for new activity
- 📁 **Original preserved** - Original session file remains unchanged

### Session Lifecycle in Our App
1. **Fresh Start**: Page refresh → no sessionId → new conversation
2. **Continuation**: Same browser session → reuse existing conversation instance
3. **Load & Resume**: Manual load → `.withSessionId()` → new forked session
4. **Clear Session**: Manual clear → reset all state → next message creates fresh

### File Structure
```
~/.claude/projects/{project-name}/
├── {original-session-id}.jsonl     # Original session (preserved)
├── {forked-session-id}.jsonl       # New session with loaded history
└── ...
```

### Implementation Notes
- Our app captures the NEW session ID returned by Claude after resumption
- This ensures subsequent messages continue with the forked session
- Frontend shows seamless continuation while backend creates new session branch
- This behavior is by design in Claude Code CLI, not a bug in our implementation