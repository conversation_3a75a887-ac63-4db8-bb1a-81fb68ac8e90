# Node.js
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.npm
.pnpm-debug.log*

# TypeScript
dist/
*.tsbuildinfo

# Environment variables
.env
.env.local
.env.*.local

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
venv/
ENV/
env.bak/
venv.bak/
.conda/

# TTS temporary files
/tmp*
*.wav
*.mp3
*.audio

# Logs
logs
*.log

# macOS
.DS_Store
.AppleDouble
.LSOverride

# Editors
.vscode/
.idea/
*.swp
*.swo
*~

# Temporary files
tmp/
temp/
/var/folders/

# Claude Code SDK cache (if any)
.claude/