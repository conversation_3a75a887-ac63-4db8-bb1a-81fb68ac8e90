# Implementation Plan: Project & Session Management System

## Overview
Transform the current single-session chat app into a multi-project, multi-session management system with a left rail interface.

## Database Design

### SQLite Setup
- **Location**: `~/.claude-chat/database.sqlite`
- **Library**: `better-sqlite3` for Node.js
- **Migration system**: Simple version-based migrations

### Schema
```sql
-- Version 1 migration
CREATE TABLE projects (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  name TEXT NOT NULL,           -- e.g., "gennext" 
  path TEXT NOT NULL UNIQUE,    -- e.g., "/Users/<USER>/Documents/gennext"
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE sessions (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  project_id INTEGER NOT NULL,
  name TEXT NOT NULL,           -- User-defined name, max 50 chars
  session_id TEXT,             -- <PERSON>'s current session ID (nullable, updates on fork)
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (project_id) REFERENCES projects(id) ON DELETE CASCADE,
  UNIQUE(project_id, name)     -- No duplicate session names per project
);

-- Indexes for performance
CREATE INDEX idx_sessions_project_id ON sessions(project_id);
CREATE INDEX idx_projects_path ON projects(path);
```

### Session Naming Constraints
- Length: 1-50 characters
- Allowed: Letters, numbers, spaces, hyphens, underscores
- No leading/trailing whitespace
- No duplicate names within same project

## Backend Changes

### New Dependencies
```json
{
  "better-sqlite3": "^9.x.x",
  "path-exists": "^5.x.x"  // for path validation
}
```

### Database Service (`src/database.ts`)
```typescript
class DatabaseService {
  // Project CRUD
  createProject(name: string, path: string): Project
  getProjects(): Project[]
  getProject(id: number): Project | null
  deleteProject(id: number): boolean
  
  // Session CRUD  
  createSession(projectId: number, name: string): Session
  getSessionsByProject(projectId: number): Session[]
  getSession(id: number): Session | null
  updateSessionId(id: number, sessionId: string): boolean
  deleteSession(id: number): boolean
  
  // Validation
  validateProjectPath(path: string): boolean
  validateSessionName(name: string): boolean
}
```

### API Endpoints (`src/server.ts`)
```typescript
// Project management
GET    /api/projects              // List all projects with sessions
POST   /api/projects              // Create new project
DELETE /api/projects/:id          // Delete project and sessions

// Session management  
POST   /api/projects/:id/sessions // Create new session
DELETE /api/sessions/:id          // Delete session
PUT    /api/sessions/:id/session-id // Update Claude session ID

// Path utilities
GET    /api/validate-path?path=X  // Validate project path
GET    /api/browse-folders?path=X // Optional: folder browser
GET    /api/autocomplete-path?partial=X // Optional: path autocomplete
```

### ChatService Updates (`src/chat.ts`)
```typescript
class ChatService {
  private projectPath: string | null = null; // Dynamic project path
  
  // New method to set project context
  setProjectPath(path: string): void
  
  // Update existing methods to use dynamic path
  // .inDirectory(this.projectPath || '/default/path')
}
```

## Frontend Changes

### New UI Layout
```
┌─────────────────────────────────────────────────────────┐
│                    Claude Voice Chat                    │
├──────────────┬──────────────────────────────────────────┤
│   Projects   │              Session View                │
│   Sidebar    │                                          │
│              │  ┌─ Session: "Feature Development" ──┐  │
│ ► Project A  │  │  Session ID: abc-123-def           │  │
│   + Session1 │  └────────────────────────────────────┘  │
│   + Session2 │                                          │
│              │  ┌─────────────────────────────────────┐ │
│ ▼ Project B  │  │         Chat Messages               │ │
│   + Session3 │  │                                     │ │
│              │  └─────────────────────────────────────┘ │
│ [+ New Proj] │                                          │
│              │  ┌─────────────────────────────────────┐ │
│              │  │  Message Input & Controls           │ │
│              │  └─────────────────────────────────────┘ │
└──────────────┴──────────────────────────────────────────┘
```

### Component Structure
```
app.js (updated)
├── ProjectSidebar
│   ├── ProjectList
│   │   ├── ProjectItem (accordion)
│   │   │   ├── SessionList  
│   │   │   └── NewSessionButton
│   │   └── NewProjectButton
│   └── Modals
│       ├── NewProjectModal
│       └── NewSessionModal
└── ChatView (existing, modified)
    ├── SessionHeader (new)
    ├── MessagesArea (existing)
    └── InputArea (existing, simplified)
```

## Implementation Steps

### Phase 1: Database Foundation
1. **Install dependencies** (`better-sqlite3`, `path-exists`)
2. **Create database service** with schema and migrations
3. **Add database initialization** to server startup
4. **Create basic CRUD operations** with tests
5. **Add path validation utilities**

### Phase 2: Backend API
1. **Implement project endpoints** (CRUD operations)
2. **Implement session endpoints** (CRUD operations)  
3. **Add path validation endpoint**
4. **Update ChatService** to accept dynamic project paths
5. **Test API endpoints** with Postman/curl

### Phase 3: Frontend Structure
1. **Update HTML layout** to include sidebar
2. **Create project sidebar component** (HTML/CSS)
3. **Implement project list UI** with accordion behavior
4. **Add modal components** for project/session creation
5. **Style with DaisyUI components**

### Phase 4: Frontend Logic
1. **Implement project management** (create, list, delete)
2. **Implement session management** (create, list, delete, switch)
3. **Add session switching logic** with chat loading
4. **Update session ID handling** for fork behavior
5. **Remove old session management UI**

### Phase 5: Polish & Testing
1. **Add path autocomplete** (if time permits)
2. **Add folder browser** (if time permits)
3. **Error handling and validation**
4. **UI polish and responsive design**
5. **End-to-end testing**

## Risk Mitigation

### Rollback Strategy
- Keep current implementation as `index-old.html`, `app-old.js`
- Database migrations are additive (no destructive changes)
- New files can be removed without affecting existing functionality

### Testing Strategy
- Test database operations in isolation first
- Test API endpoints before frontend integration
- Progressive enhancement (basic functionality first, UX improvements later)

### File Backup
- Current working files will be preserved with `-old` suffix
- Database will be versioned with migrations
- Git commits at each major milestone

## File Changes Summary

### New Files
- `src/database.ts` - Database service and schema
- `src/migrations/` - Database migration files
- Database file: `~/.claude-chat/database.sqlite`

### Modified Files
- `src/server.ts` - Add new API endpoints
- `src/chat.ts` - Dynamic project paths
- `public/index.html` - New layout with sidebar
- `public/app.js` - Project/session management logic
- `package.json` - New dependencies

### Removed Elements
- Session loading/clearing UI in current interface
- Hardcoded project paths

---

## Approval Request

This plan provides:
- ✅ Comprehensive database design with proper constraints
- ✅ Clear API structure for all operations  
- ✅ Step-by-step implementation phases
- ✅ Risk mitigation and rollback strategy
- ✅ File structure and component organization

**Ready to proceed with implementation?** I recommend starting with Phase 1 (Database Foundation) and getting your approval at each phase completion.