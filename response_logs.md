🌊 SSE streaming chat request: let's check the
   status and pull down the latest to make sure we are up to date
  🚀 Calling chatService.sendMessage with SSE...
  🌊 ChatService.sendMessageStreaming called (PERSISTENT + STREAMING mode)
  📝 Message: let's check the status and pull down the latest to make sure we are up to date
  🆔 Current sessionId: 6a0c3fd0-c926-457b-af9f-b9384adb77dc
  💼 Conversation exists: true
  📤 Sending message to persistent conversation...
  ⏳ Waiting for message completion...
  📦 Received stream message - FULL DATA: {
    msg: '{\n' +
      '  "type": "system",\n' +
      '  "subtype": "init",\n' +
      '  "data": {\n' +
      '    "type": "system",\n' +
      '    "subtype": "init",\n' +
      '    "cwd": "/Users/<USER>/Documents/gennext",\n' +
      '    "session_id": "6a0c3fd0-c926-457b-af9f-b9384adb77dc",\n' +
      '    "tools": [\n' +
      '      "Task",\n' +
      '      "Bash",\n' +
      '      "Glob",\n' +
      '      "Grep",\n' +
      '      "LS",\n' +
      '      "ExitPlanMode",\n' +
      '      "Read",\n' +
      '      "Edit",\n' +
      '      "MultiEdit",\n' +
      '      "Write",\n' +
      '      "NotebookRead",\n' +
      '      "NotebookEdit",\n' +
      '      "WebFetch",\n' +
      '      "TodoWrite",\n' +
      '      "WebSearch"\n' +
      '    ],\n' +
      '    "mcp_servers": [],\n' +
      '    "model": "claude-sonnet-4-20250514",\n' +
      '    "permissionMode": "bypassPermissions",\n' +
      '    "slash_commands": [\n' +
      '      "add-dir",\n' +
      '      "agents",\n' +
      '      "clear",\n' +
      '      "compact",\n' +
      '      "config",\n' +
      '      "cost",\n' +
      '      "doctor",\n' +
      '      "exit",\n' +
      '      "help",\n' +
      '      "ide",\n' +
      '      "init",\n' +
      '      "install-github-app",\n' +
      '      "mcp",\n' +
      '      "memory",\n' +
      '      "migrate-installer",\n' +
      '      "model",\n' +
      '      "pr-comments",\n' +
      '      "release-notes",\n' +
      '      "resume",\n' +
      '      "status",\n' +
      '      "bug",\n' +
      '      "review",\n' +
      '      "terminal-setup",\n' +
      '      "upgrade",\n' +
      '      "vim",\n' +
      '      "permissions",\n' +
      '      "hooks",\n' +
      '      "export",\n' +
      '      "logout",\n' +
      '      "login"\n' +
      '    ],\n' +
      '    "apiKeySource": "none"\n' +
      '  },\n' +
      '  "session_id": "6a0c3fd0-c926-457b-af9f-b9384adb77dc"\n' +
      '}',
    sessionId: '6a0c3fd0-c926-457b-af9f-b9384adb77dc'
  }
  ⚠️ Non-assistant message type: system
  📦 Received stream message - FULL DATA: {
    msg: '{\n' +
      '  "type": "assistant",\n' +
      '  "content": [\n' +
      '    {\n' +
      '      "type": "text",\n' +
      `      "text": "I'll check the git status and pull the latest changes to make sure we're up to date."\n` +
      '    }\n' +
      '  ],\n' +
      '  "session_id": "6a0c3fd0-c926-457b-af9f-b9384adb77dc"\n' +
      '}',
    sessionId: '6a0c3fd0-c926-457b-af9f-b9384adb77dc'
  }
  ✅ Processing assistant message
  📝 Text block found: I'll check the git status and pull the latest chan...
  📦 SSE sending chunk 1: { length: 84, preview: "I'll check the git status and ..." }
  📤 Chunk sent to UI, current message length so far: 84
  📦 Received stream message - FULL DATA: {
    msg: '{\n' +
      '  "type": "assistant",\n' +
      '  "content": [\n' +
      '    {\n' +
      '      "type": "tool_use",\n' +
      '      "id": "toolu_01V5Wubnqd6p35GuniZxifQB",\n' +
      '      "name": "Bash",\n' +
      '      "input": {\n' +
      '        "command": "git status",\n' +
      '        "description": "Check current git status"\n' +
      '      }\n' +
      '    }\n' +
      '  ],\n' +
      '  "session_id": "6a0c3fd0-c926-457b-af9f-b9384adb77dc"\n' +
      '}',
    sessionId: '6a0c3fd0-c926-457b-af9f-b9384adb77dc'
  }
  ✅ Processing assistant message

  🔧 Bash: {
    "command": "git status",
    "description": "Check current git status"
  }
  🔧 SSE sending tool use: Bash
  📦 Received stream message - FULL DATA: {
    msg: '{\n' +
      '  "type": "assistant",\n' +
      '  "content": [\n' +
      '    {\n' +
      '      "type": "tool_use",\n' +
      '      "id": "toolu_012Ah1215okAxLUfeQNoq4yG",\n' +
      '      "name": "Bash",\n' +
      '      "input": {\n' +
      '        "command": "git pull origin main",\n' +
      '        "description": "Pull latest changes from main branch"\n' +
      '      }\n' +
      '    }\n' +
      '  ],\n' +
      '  "session_id": "6a0c3fd0-c926-457b-af9f-b9384adb77dc"\n' +
      '}',
    sessionId: '6a0c3fd0-c926-457b-af9f-b9384adb77dc'
  }
  ✅ Processing assistant message

  🔧 Bash: {
    "command": "git pull origin main",
    "description": "Pull latest changes from main branch"
  }
  🔧 SSE sending tool use: Bash
  📦 Received stream message - FULL DATA: {
    msg: '{\n' +
      '  "type": "user",\n' +
      '  "content": [\n' +
      '    {\n' +
      '      "tool_use_id": "toolu_01V5Wubnqd6p35GuniZxifQB",\n' +
      '      "type": "tool_result",\n' +
      `      "content": "On branch reorganize-chat-companion-cards\\nYour branch is up to date with 
  'origin/reorganize-chat-companion-cards'.\\n\\nnothing to commit, working tree clean",\n` +
      '      "is_error": false\n' +
      '    }\n' +
      '  ],\n' +
      '  "session_id": "6a0c3fd0-c926-457b-af9f-b9384adb77dc"\n' +
      '}',
    sessionId: '6a0c3fd0-c926-457b-af9f-b9384adb77dc'
  }
  ⚠️ Non-assistant message type: user
  📦 Received stream message - FULL DATA: {
    msg: '{\n' +
      '  "type": "user",\n' +
      '  "content": [\n' +
      '    {\n' +
      '      "tool_use_id": "toolu_012Ah1215okAxLUfeQNoq4yG",\n' +
      '      "type": "tool_result",\n' +
      '      "content": "Updating b663329..e248ba9\\nFast-forward\\nFrom 
  https://github.com/JosephPeters/gennext\\n * branch            main       -> FETCH_HEAD\\n   339490d..e248ba9  
  main       -> origin/main",\n' +
      '      "is_error": false\n' +
      '    }\n' +
      '  ],\n' +
      '  "session_id": "6a0c3fd0-c926-457b-af9f-b9384adb77dc"\n' +
      '}',
    sessionId: '6a0c3fd0-c926-457b-af9f-b9384adb77dc'
  }
  ⚠️ Non-assistant message type: user
  📦 Received stream message - FULL DATA: {
    msg: '{\n' +
      '  "type": "assistant",\n' +
      '  "content": [\n' +
      '    {\n' +
      '      "type": "text",\n' +
      '      "text": "Great! I pulled the latest changes from main. Your current branch 
  `reorganize-chat-companion-cards` is clean and we now have the updated main branch. \\n\\nWould you like me to 
  merge the latest main changes into your feature branch, or what would you like to work on next?"\n' +
      '    }\n' +
      '  ],\n' +
      '  "session_id": "6a0c3fd0-c926-457b-af9f-b9384adb77dc"\n' +
      '}',
    sessionId: '6a0c3fd0-c926-457b-af9f-b9384adb77dc'
  }
  ✅ Processing assistant message
  📝 Text block found: Great! I pulled the latest changes from main. Your...
  📦 SSE sending chunk 2: { length: 268, preview: 'Great! I pulled the latest cha...' }
  📤 Chunk sent to UI, current message length so far: 352
  📦 Received stream message - FULL DATA: {
    msg: '{\n' +
      '  "type": "result",\n' +
      '  "subtype": "success",\n' +
      '  "is_error": false,\n' +
      '  "content": "Great! I pulled the latest changes from main. Your current branch 
  `reorganize-chat-companion-cards` is clean and we now have the updated main branch. \\n\\nWould you like me to 
  merge the latest main changes into your feature branch, or what would you like to work on next?",\n' +
      '  "result": "Great! I pulled the latest changes from main. Your current branch 
  `reorganize-chat-companion-cards` is clean and we now have the updated main branch. \\n\\nWould you like me to 
  merge the latest main changes into your feature branch, or what would you like to work on next?",\n' +
      '  "usage": {\n' +
      '    "input_tokens": 10,\n' +
      '    "cache_creation_input_tokens": 691,\n' +
      '    "cache_read_input_tokens": 31017,\n' +
      '    "output_tokens": 224,\n' +
      '    "server_tool_use": {\n' +
      '      "web_search_requests": 0\n' +
      '    },\n' +
      '    "service_tier": "standard"\n' +
      '  },\n' +
      '  "session_id": "6a0c3fd0-c926-457b-af9f-b9384adb77dc"\n' +
      '}',
    sessionId: '6a0c3fd0-c926-457b-af9f-b9384adb77dc'
  }
  🏁 Message completed with result
  🌊 Streaming completed, total chunks for this message: 4
  🌊 Streaming completed, this message length: 352
  🔧 Tool uses captured: 2
  🏁 Final sessionId: 6a0c3fd0-c926-457b-af9f-b9384adb77dc
  ✅ SSE streaming completed on server
  📊 SSE statistics: { totalChunks: 2, fullResponseLength: 352, toolUses: 2 }