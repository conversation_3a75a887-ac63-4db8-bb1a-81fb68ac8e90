# Claude Chat with Voice Implementation Guide

A simple chat application using Claude Code SDK (TypeScript) and Kokoro TTS (Python) with room to grow.

## Resources

- **<PERSON> Code SDK (TypeScript):** https://github.com/instantlyeasy/claude-code-sdk-ts
- **Kokoro TTS (Python):** https://github.com/hexgrad/kokoro

## Architecture Overview

```
┌─────────────────┐    HTTP/REST    ┌─────────────────┐
│   Chat App      │ ◄──────────────► │  TTS Service    │
│   (TypeScript)  │                  │   (Python)      │
│                 │                  │                 │
│ - Claude SDK    │                  │ - Kokoro TTS    │
│ - Web Interface │                  │ - FastAPI       │
│ - Audio Player  │                  │ - Audio Gen     │
└─────────────────┘                  └─────────────────┘
```

## Project Structure

```
claude-chat/
├── chat-app/           # TypeScript/Node.js app
│   ├── src/
│   │   ├── chat.ts     # Claude integration
│   │   ├── audio.ts    # Audio handling
│   │   └── server.ts   # Express server
│   ├── public/
│   │   ├── index.html  # Simple chat UI
│   │   └── app.js      # Frontend JS
│   └── package.json
├── tts-service/        # Python TTS service
│   ├── main.py         # FastAPI server
│   ├── tts.py          # Kokoro wrapper
│   └── requirements.txt
└── docker-compose.yml  # Optional: containerization
```

## Phase 1: Basic Implementation

### 1. TTS Service (Python)

**requirements.txt**
```
fastapi
uvicorn
kokoro-tts
```

**main.py**
```python
from fastapi import FastAPI
from fastapi.responses import FileResponse
from tts import generate_speech
import tempfile
import os

app = FastAPI()

@app.post("/synthesize")
async def synthesize_text(text: str):
    # MVP: hardcoded voice, WAV format (best quality/compatibility)
    audio_path = generate_speech(text, voice="af_heart")
    return FileResponse(audio_path, media_type="audio/wav")

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8001)
```

**tts.py**
```python
from kokoro import KPipeline
import tempfile
import os

pipeline = KPipeline(lang_code='a')  # American English

def generate_speech(text: str, voice: str = "af_heart") -> str:
    generator = pipeline(text, voice=voice, speed=1)
    
    # Save to temporary file
    temp_file = tempfile.NamedTemporaryFile(suffix='.wav', delete=False)
    # Write audio data to temp_file
    # Return path for FastAPI to serve
    return temp_file.name
```

### 2. Chat App (TypeScript)

**package.json**
```json
{
  "name": "claude-chat",
  "version": "1.0.0",
  "dependencies": {
    "claude-code-sdk-ts": "latest",
    "express": "^4.18.0",
    "axios": "^1.6.0"
  },
  "scripts": {
    "dev": "tsx src/server.ts",
    "build": "tsc"
  }
}
```

**src/chat.ts**
```typescript
import { claude } from 'claude-code-sdk-ts';

export class ChatService {
  private session = claude().withModel('sonnet');
  private sessionId: string | null = null;

  async sendMessage(message: string): Promise<string> {
    try {
      let query = this.session.query(message);
      
      // Use existing session if available
      if (this.sessionId) {
        query = query.withSessionId(this.sessionId);
      }
      
      const response = await query.asText();
      
      // Store session ID for conversation continuity
      if (!this.sessionId) {
        this.sessionId = await this.session.query('').getSessionId();
      }
      
      return response;
    } catch (error) {
      console.error('Claude error:', error);
      throw new Error('Failed to get Claude response');
    }
  }

  // For future multi-chat support
  async createNewSession(): Promise<void> {
    this.session = claude().withModel('sonnet');
    this.sessionId = null;
  }
}
```

**src/audio.ts**
```typescript
import axios from 'axios';

export class AudioService {
  private ttsBaseUrl = 'http://localhost:8001';

  async synthesizeSpeech(text: string): Promise<string> {
    try {
      const response = await axios.post(`${this.ttsBaseUrl}/synthesize`, {
        text
      }, {
        responseType: 'blob'
      });

      // Convert blob to data URL for audio element
      const blob = new Blob([response.data], { type: 'audio/wav' });
      return URL.createObjectURL(blob);
    } catch (error) {
      console.error('TTS error:', error);
      throw new Error('Failed to generate speech');
    }
  }
}
```

**src/server.ts**
```typescript
import express from 'express';
import { ChatService } from './chat';
import { AudioService } from './audio';

const app = express();
const chatService = new ChatService();
const audioService = new AudioService();

app.use(express.json());
app.use(express.static('public'));

app.post('/api/chat', async (req, res) => {
  try {
    const { message } = req.body;
    const response = await chatService.sendMessage(message);
    res.json({ response });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

app.post('/api/speak', async (req, res) => {
  try {
    const { text } = req.body;
    const audioUrl = await audioService.synthesizeSpeech(text);
    res.json({ audioUrl });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

app.listen(3000, () => {
  console.log('Chat app running on http://localhost:3000');
});
```

**public/index.html**
```html
<!DOCTYPE html>
<html>
<head>
    <title>Claude Voice Chat</title>
    <style>
        .chat-container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .messages { height: 400px; overflow-y: scroll; border: 1px solid #ccc; padding: 10px; }
        .message { margin: 10px 0; }
        .user { color: blue; }
        .claude { color: green; }
        .input-area { margin-top: 20px; }
        input[type="text"] { width: 70%; padding: 10px; }
        button { padding: 10px 15px; margin-left: 10px; }
    </style>
</head>
<body>
    <div class="chat-container">
        <h1>Claude Voice Chat</h1>
        <div id="messages" class="messages"></div>
        <div class="input-area">
            <input type="text" id="messageInput" placeholder="Type your message...">
            <button onclick="sendMessage()">Send</button>
            <button onclick="toggleVoice()" id="voiceBtn">Voice: OFF</button>
        </div>
    </div>
    <script src="app.js"></script>
</body>
</html>
```

**public/app.js**
```javascript
let voiceEnabled = false;

async function sendMessage() {
    const input = document.getElementById('messageInput');
    const message = input.value.trim();
    if (!message) return;

    addMessage('You', message, 'user');
    input.value = '';

    try {
        const response = await fetch('/api/chat', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ message })
        });

        const data = await response.json();
        addMessage('Claude', data.response, 'claude');

        if (voiceEnabled) {
            await speakText(data.response);
        }
    } catch (error) {
        addMessage('Error', error.message, 'error');
    }
}

function addMessage(sender, text, className) {
    const messages = document.getElementById('messages');
    const div = document.createElement('div');
    div.className = `message ${className}`;
    div.innerHTML = `<strong>${sender}:</strong> ${text}`;
    messages.appendChild(div);
    messages.scrollTop = messages.scrollHeight;
}

async function speakText(text) {
    try {
        const response = await fetch('/api/speak', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ text })
        });

        const data = await response.json();
        const audio = new Audio(data.audioUrl);
        audio.play();
    } catch (error) {
        console.error('Speech synthesis failed:', error);
    }
}

function toggleVoice() {
    voiceEnabled = !voiceEnabled;
    const btn = document.getElementById('voiceBtn');
    btn.textContent = `Voice: ${voiceEnabled ? 'ON' : 'OFF'}`;
}

// Enter key support
document.getElementById('messageInput').addEventListener('keypress', function(e) {
    if (e.key === 'Enter') {
        sendMessage();
    }
});
```

## Design Notes

### Session Management
- **MVP**: Single chat session with automatic history via Claude Code SDK
- **Future**: Multi-chat support ready via `ChatService.createNewSession()`
- **History**: Handled automatically by Claude Code CLI - no manual storage needed

### Audio Approach  
- **Voice**: Hardcoded `af_heart` for MVP (Kokoro supports multiple voices)
- **Format**: WAV for best quality/compatibility
- **Timing**: TTS after complete Claude response (Kokoro supports ~210x real-time)
- **Fallback**: Text-first design - audio is enhancement, not requirement

## Getting Started

1. **Setup TTS Service:**
   ```bash
   cd tts-service
   pip install -r requirements.txt
   python main.py
   ```

2. **Setup Chat App:**
   ```bash
   cd chat-app
   npm install
   npm run dev
   ```

3. **Open browser:** http://localhost:3000

## Phase 2: Growth Features

### Planned Enhancements
- **Voice Input:** Add speech-to-text for voice conversations
- **Multiple Voices:** Voice selection UI
- **Chat History:** Persistent conversation storage
- **Real-time:** Streaming TTS as Claude responds (Kokoro supports ~210x real-time)
- **Customization:** Themes, voice settings, Claude personas

### Technical Improvements
- **Error Handling:** Retry logic, graceful degradation, fallback to text-only
- **Performance:** Audio caching, response streaming
- **Audio Cleanup:** TODO - implement temp file cleanup strategy for TTS service
- **Testing:** Unit tests, integration tests

## Development Tips

1. **Start Simple:** Get basic text chat working first
2. **Iterate:** Add voice features incrementally
3. **Test Often:** Both services running simultaneously
4. **Monitor:** Log requests and errors
5. **Document:** API endpoints and configurations

## Troubleshooting

- **Claude SDK:** Check API keys and permissions
- **Kokoro:** Verify Python dependencies and model files
- **CORS:** Add CORS middleware if needed
- **Audio:** Check browser audio permissions
- **Ports:** Ensure 3000 and 8001 are available