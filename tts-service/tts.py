from kokoro import KPipeline
import tempfile
import os
import numpy as np
import soundfile as sf

try:
    pipeline = KPipeline(lang_code='a')  # American English
    print("✅ Kokoro pipeline initialized successfully")
except Exception as e:
    print(f"❌ Failed to initialize Kokoro pipeline: {e}")
    pipeline = None

def generate_speech(text: str, voice: str = "af_sky") -> str:
    if pipeline is None:
        raise Exception("Kokoro pipeline not initialized")
    
    print(f"🎵 Generating speech for: '{text}' with voice: {voice}")
    
    try:
        generator = pipeline(text, voice=voice, speed=1.0)
        
        # Collect all audio data
        audio_chunks = []
        for i, segment in enumerate(generator):
            audio_tensor = segment.audio
            audio_numpy = audio_tensor.detach().cpu().numpy()
            print(f"📦 Processing chunk {i}, audio shape: {audio_numpy.shape}")
            audio_chunks.extend(audio_numpy)  # extend, not append
        
        if not audio_chunks:
            raise Exception("No audio data generated")
        
        # Convert to numpy array (audio_chunks is already a flat list)
        full_audio = np.array(audio_chunks)
        print(f"🔊 Final audio shape: {full_audio.shape}")
        
        # Save to temporary file using soundfile
        temp_file = tempfile.NamedTemporaryFile(suffix='.wav', delete=False)
        temp_file.close()  # Close file handle so soundfile can write to it
        
        # Write audio with proper sample rate (24kHz for Kokoro)
        sf.write(temp_file.name, full_audio, 24000)
        
        print(f"💾 Audio saved to: {temp_file.name}")
        return temp_file.name
        
    except Exception as e:
        print(f"❌ Error generating speech: {e}")
        raise Exception(f"Failed to generate speech: {str(e)}")