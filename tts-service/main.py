from fastapi import <PERSON><PERSON><PERSON>
from fastapi.responses import FileResponse
from fastapi.middleware.cors import CORSMiddleware
from tts import generate_speech
import tempfile
import os

app = FastAPI()

# Add CORS middleware for local development and network access
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Allow all origins for local development
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.post("/synthesize")
async def synthesize_text(request: dict):
    text = request.get("text", "")
    if not text:
        return {"error": "No text provided"}
    
    # MVP: hardcoded voice, WAV format (best quality/compatibility)
    audio_path = generate_speech(text, voice="af_sky")
    return FileResponse(
        audio_path, 
        media_type="audio/wav",
        headers={
            "Content-Disposition": "inline; filename=speech.wav",
            "Cache-Control": "no-cache"
        }
    )

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8001)